using SqlSugar;

namespace tkapi.Models
{
    /// <summary>
    /// 用户表实体
    /// </summary>
    [SugarTable("tb_user")]
    [SugarIndex("idx_user_appid", "appid", OrderByType.Asc, true)]
    public class UserEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "sysid")]
        public int SysId { get; set; }

        /// <summary>
        /// 应用ID，唯一索引
        /// </summary>
        [SugarColumn(ColumnName = "appid", IsNullable = false, Length = 50, DefaultValue = "")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 密钥
        /// </summary>
        [SugarColumn(ColumnName = "secret_key", IsNullable = true, Length = 400, DefaultValue = "")]
        public string? SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// 允许访问的服务，多个以逗号分隔
        /// </summary>
        [SugarColumn(ColumnName = "allow", IsNullable = true, Length = 200, DefaultValue = "")]
        public string? Allow { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用，1-启用，0-禁用
        /// </summary>
        [SugarColumn(ColumnName = "is_enable", IsNullable = true, DefaultValue = "1")]
        public int? IsEnable { get; set; } = 1;

        /// <summary>
        /// 有效期
        /// </summary>
        [SugarColumn(ColumnName = "vaild_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? VaildTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "remark", IsNullable = true, Length = 100, DefaultValue = "")]
        public string? Remark { get; set; } = string.Empty;
    }
} 