using SqlSugar;

namespace tkapi.Models
{
    /// <summary>
    /// 设备表实体
    /// </summary>
    [SugarTable("tb_device")]
    [SugarIndex("idx_device_clientid", "clientid", OrderByType.Asc, true)]
    public class DeviceEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "sysid")]
        public int SysId { get; set; }

        /// <summary>
        /// 客户端ID，唯一索引
        /// </summary>
        [SugarColumn(ColumnName = "clientid", IsNullable = false, Length = 50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 在线状态，0-离线，1-在线
        /// </summary>
        [SugarColumn(ColumnName = "is_online", IsNullable = true, DefaultValue = "0")]
        public int? IsOnline { get; set; } = 0;

        /// <summary>
        /// 用户名
        /// </summary>
        [SugarColumn(ColumnName = "username", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [SugarColumn(ColumnName = "password", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Password { get; set; } = string.Empty;

        /// <summary>
        /// IMEI
        /// </summary>
        [SugarColumn(ColumnName = "imei", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Imei { get; set; } = string.Empty;

        /// <summary>
        /// IMSI
        /// </summary>
        [SugarColumn(ColumnName = "imsi", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Imsi { get; set; } = string.Empty;

        /// <summary>
        /// ICCID
        /// </summary>
        [SugarColumn(ColumnName = "iccid", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Iccid { get; set; } = string.Empty;

        /// <summary>
        /// 主机联动节点，索引
        /// </summary>
        [SugarColumn(ColumnName = "hostid", IsNullable = false, Length = 50, DefaultValue = "", IndexGroupNameList = new string[] { "hostid" })]
        public string HostId { get; set; } = string.Empty;

        /// <summary>
        /// 推送Unilink，索引
        /// </summary>
        [SugarColumn(ColumnName = "unilink", IsNullable = false, Length = 50, DefaultValue = "", IndexGroupNameList = new string[] { "unilink" })]
        public string Unilink { get; set; } = string.Empty;

        /// <summary>
        /// 推送Dispatch，索引
        /// </summary>
        [SugarColumn(ColumnName = "dispatch", IsNullable = false, Length = 50, DefaultValue = "", IndexGroupNameList = new string[] { "dispatch" })]
        public string Dispatch { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        [SugarColumn(ColumnName = "product", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Product { get; set; } = string.Empty;

        /// <summary>
        /// 分支版本
        /// </summary>
        [SugarColumn(ColumnName = "branchv", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? BranchV { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        [SugarColumn(ColumnName = "version", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Version { get; set; } = string.Empty;

        /// <summary>
        /// 最低要求版本
        /// </summary>
        [SugarColumn(ColumnName = "min_version", IsNullable = true, Length = 50, DefaultValue = "1.0.10")]
        public string? MinVersion { get; set; } = string.Empty;

        /// <summary>
        /// 遥测数据
        /// </summary>
        [SugarColumn(ColumnName = "telemetry", IsNullable = true, ColumnDataType = "TEXT")]
        public string? Telemetry { get; set; }

        /// <summary>
        /// 属性数据
        /// </summary>
        [SugarColumn(ColumnName = "attributes", IsNullable = true, ColumnDataType = "TEXT")]
        public string? Attributes { get; set; }

        /// <summary>
        /// 连接时间
        /// </summary>
        [SugarColumn(ColumnName = "connect_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? ConnectTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 断开连接时间
        /// </summary>
        [SugarColumn(ColumnName = "disconnect_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? DisconnectTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 断开连接原因
        /// </summary>
        [SugarColumn(ColumnName = "disconnect_reason", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? DisconnectReason { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "update_time", IsNullable = true, DefaultValue = "now() ON UPDATE CURRENT_TIMESTAMP")]
        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
} 