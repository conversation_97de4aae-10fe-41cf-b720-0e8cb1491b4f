# IoT MQTT 服务 API 文档

本文档详细说明了IoT MQTT服务提供的所有API接口，供其他子系统集成使用。

## 目录

- [状态监控接口](#状态监控接口)
- [认证与授权接口](#认证与授权接口)
- [设备管理接口](#设备管理接口)
- [MQTT消息接口](#mqtt消息接口)
- [北电云表接口](#北电云表接口)

## 状态监控接口

### 获取服务状态

- **URL**: `/status`
- **方法**: `GET`
- **描述**: 获取服务基本运行状态
- **响应**:
  ```json
  {
    "status": "Running",
    "timestamp": "2023-09-01T12:34:56Z",
    "version": "1.0.0"
  }
  ```

### 健康检查

- **URL**: `/health`
- **方法**: `GET`
- **描述**: 符合标准的健康检查端点，用于容器编排系统监控
- **响应**:
  - 状态码 `200`: 服务健康
  - 状态码 `503`: 服务不健康

### 获取MQTT连接状态

- **URL**: `/mqtt/status`
- **方法**: `GET`
- **描述**: 获取MQTT客户端连接详细状态
- **响应**:
  ```json
  {
    "isConnected": true,
    "reconnectAttempts": 0,
    "timestamp": "2023-09-01T12:34:56Z"
  }
  ```

### 获取GrpID映射状态

- **URL**: `/mapper/status`
- **方法**: `GET`
- **描述**: 获取GrpID映射服务状态信息
- **响应**:
  ```json
  {
    "mappingCount": 42,
    "timestamp": "2023-09-01T12:34:56Z"
  }
  ```

## 认证与授权接口

### 用户登录

- **URL**: `/oauth/login`
- **方法**: `POST`
- **描述**: 验证用户凭证并返回JWT令牌
- **请求体**:
  ```json
  {
    "username": "app_id",
    "password": "secret_key"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expire": 1627184142
  }
  ```
- **错误响应**:
  - 状态码 `401`: 认证失败
    ```json
    {
      "success": false,
      "message": "无效的用户名或密码"
    }
    ```
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "登录失败：发生内部错误"
    }
    ```

### MQTT客户端认证

- **URL**: `/mqtt/auth`
- **方法**: `POST`
- **描述**: 验证MQTT客户端连接权限（供EMQX调用）
- **请求体**:
  ```json
  {
    "clientId": "device_client_id",
    "username": "device_username",
    "password": "device_password"
  }
  ```
- **响应**:
  ```json
  {
    "result": "allow"
  }
  ```
  或
  ```json
  {
    "result": "deny"
  }
  ```

### 设备授权

- **URL**: `/core/device_license`
- **方法**: `GET`
- **描述**: 为设备生成MQTT连接凭证
- **参数**:
  - `imei`: 设备IMEI号 (必需)
  - `imsi`: SIM卡IMSI号 (可选)
  - `iccid`: SIM卡ICCID号 (可选)
  - `ekey`: 应用密钥 (必需)
- **响应**:
  ```json
  {
    "clientid": "i123456789012345",
    "username": "i123456789012345",
    "password": "generated_password"
  }
  ```
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "无效的应用密钥或权限不足"
    }
    ```

### 网关授权

- **URL**: `/core/gateway_license`
- **方法**: `GET`
- **描述**: 生成网关授权文件
- **参数**:
  - `cpud`: 网关CPU ID (必需)
  - `uuid`: 网关UUID (必需)
  - `ekey`: 应用密钥 (必需)
- **响应**: 二进制授权文件（licx格式）
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "无效的应用密钥或权限不足"
    }
    ```

## 设备管理接口

### 设备更新

- **URL**: `/mqtt/update`
- **方法**: `GET`
- **描述**: 检查设备版本并推送固件更新通知
- **参数**:
  - `clientid`: 设备客户端ID (必需)
- **响应**:
  ```json
  {
    "success": true,
    "message": "已发送更新通知",
    "updateInfo": {
      "ekey": "device_id",
      "burl": "http://update.server.com/files/v1.0.1",
      "file": ["firmware.bin", "config.json"]
    }
  }
  ```
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "设备不存在或无需更新"
    }
    ```

## MQTT消息接口

### 发布MQTT消息

- **URL**: `/mqtt/publish`
- **方法**: `POST`
- **描述**: 直接向MQTT代理发布消息
- **请求体**:
  ```json
  {
    "topic": "test/topic",
    "payload": "消息内容",
    "qos": 0,
    "retain": false
  }
  ```
- **参数说明**:
  - `topic` (必需): MQTT主题，支持使用逗号分隔多个主题进行批量发布
  - `payload` (可选): 消息载荷，默认为空字符串
  - `qos` (可选): 服务质量等级，支持0、1、2，默认为0
  - `retain` (可选): 是否保留消息，默认为false
- **批量发布**:
  - 当`topic`参数包含逗号时，系统会将同一消息发布到多个主题
  - 示例: `"topic": "sensors/temp,sensors/humidity,sensors/pressure"`
  - 响应会显示成功发布的主题数量
- **响应**:
  ```json
  {
    "success": true,
    "message": "消息已发布"
  }
  ```
  或批量发布时：
  ```json
  {
    "success": true,
    "message": "已向3个主题发布消息"
  }
  ```
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "消息发布失败",
      "error": "错误详情"
    }
    ```

## 北电云表接口

### 获取北电云表令牌

- **URL**: `/oauth/bdyb`
- **方法**: `GET`
- **描述**: 获取北电云表认证令牌
- **响应**:
  ```json
  {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expire": 1627184142
  }
  ```
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "北电云表配置缺失"
    }
    ```
  - 状态码 `500`: 服务器错误

### 处理北电云表响应

- **URL**: `/bdyb/beidian-response`
- **方法**: `POST`
- **描述**: 处理北电云表响应并转发
- **请求体**:
  ```json
  {
    "commandId": "command123",
    "devNo": "device001",
    "resultParams": { "key": "value" }
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "响应已处理"
  }
  ```
- **错误响应**:
  - 状态码 `400`: 请求错误
    ```json
    {
      "success": false,
      "message": "请求格式无效"
    }
    ```

## 集成指南

### MQTT主题订阅

要接收设备消息，您的系统需要订阅以下MQTT主题：

1. **设备响应消息**:
   - 主题格式: `v1/unilink/${user}/${imei}/rpc/response`
   - 消息格式:
     ```json
     {
       "taskid": "uuid",
       "status": "success",
       "method": "setcommand",
       "result": { "key": "value" }
     }
     ```

2. **设备上报数据**:
   - 主题格式: `v1/unilink/${user}/${imei}/report`
   - 消息格式: 根据设备类型不同而变化

3. **设备配置信息**:
   - 主题格式: `v1/unilink/${user}/${imei}/config`
   - 消息格式: 根据设备类型不同而变化

4. **设备连接事件**:
   - 主题格式: `v1/unilink/${user}/${imei}/client_connected`
   - 消息格式:
     ```json
     {
       "clientId": "i123456789012345",
       "username": "device_username",
       "peerName": "192.168.1.100",
       "connected": "2023-09-01T12:34:56Z",
       "protocol": "MQTT",
       "keepAlive": 60
     }
     ```

5. **设备断开连接事件**:
   - 主题格式: `v1/unilink/${user}/${imei}/client_disconnected`
   - 消息格式:
     ```json
     {
       "clientId": "i123456789012345",
       "reason": "normal",
       "timestamp": 1627184142
     }
     ```

### MQTT消息发送

要向设备发送指令，您的系统需要发布消息到以下主题：

1. **设备指令请求**:
   - 主题格式: `v1/unilink/${user}/${imei}/rpc/request`
   - 消息格式:
     ```json
     {
       "taskid": "unique_task_id",
       "method": "setcommand",
       "params": {
         "cmd": "设备指令内容"
       }
     }
     ```
   - 说明: `taskid`为可选，如未提供会自动生成

### Redis消息订阅

您的系统可以通过Redis发布/订阅(Pub/Sub)机制接收实时消息：

1. **设备响应消息**:
   - 通道名: 设备响应主题 (例如: `device/i123456789012345/respone/relay`)
   - 消息格式: 原始设备响应JSON

2. **北电云表响应**:
   - 通道名: commandId (例如: `command123`)
   - 消息格式: 北电云表响应参数JSON

3. **任务ID关联响应**:
   - 通道名: `guid.{taskId}` (例如: `guid.task123`)
   - 消息格式: 完整的响应消息JSON

### 集成示例

#### 1. 发送设备指令并接收响应

```javascript
// 1. 发送设备指令
const taskId = "task_" + Date.now();
const message = {
  taskid: taskId,
  method: "setcommand",
  params: {
    cmd: "AT+CGMI"
  }
};

// 发布到MQTT主题
mqttClient.publish(`v1/unilink/modem/123456789012345/rpc/request`, JSON.stringify(message));

// 2. 订阅响应主题
mqttClient.subscribe(`v1/unilink/modem/123456789012345/rpc/response`);

// 3. 处理响应
mqttClient.on('message', (topic, message) => {
  if (topic === `v1/unilink/modem/123456789012345/rpc/response`) {
    const response = JSON.parse(message.toString());
    if (response.taskid === taskId) {
      console.log("收到设备响应:", response);
    }
  }
});
```

#### 2. 通过Redis订阅设备响应

```javascript
// 使用Redis客户端订阅通道
const redis = require("redis");
const subscriber = redis.createClient({
  url: "redis://127.0.0.1:6379"
});

// 订阅特定任务ID的响应
subscriber.subscribe(`guid.task_123456`);

// 处理接收到的消息
subscriber.on("message", (channel, message) => {
  console.log(`收到来自通道 ${channel} 的消息:`, JSON.parse(message));
});
```

## 错误码说明

| 状态码 | 说明 |
|-------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误或格式无效 |
| 401 | 认证失败，凭证无效 |
| 403 | 权限不足，无法访问资源 |
| 404 | 请求的资源不存在 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用，通常是MQTT连接断开 |

## 安全说明

1. **认证要求**:
   - 除了状态监控接口外，所有API都需要认证
   - 使用JWT令牌进行API认证，在请求头中添加`Authorization: Bearer {token}`

2. **令牌获取**:
   - 通过`/oauth/login`接口获取JWT令牌
   - 令牌有效期默认为24小时

3. **安全建议**:
   - 使用HTTPS保护API通信
   - 定期轮换应用密钥
   - 不要在客户端代码中硬编码密钥
   - 使用最小权限原则配置应用权限 