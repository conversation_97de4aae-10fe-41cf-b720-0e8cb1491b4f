using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using tksrv.dal;
using tksrv.model;
using tkapi.Models;
using NLog;
using Flurl.Http;
using StackExchange.Redis;

namespace tksrv.bll
{
    public class DeviceMQTTBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<object> license(string imei, string imsi, string iccid, string ekey)
        {
            var devk = await ProductDAL.queryDeviceKey(ekey);
            if (devk == null || devk.is_enable != 1)
            {
                throw new ArgumentException();
            }
            if (!Regex.IsMatch(imei, @"^\d+$"))
            {
                throw new ArgumentException();
            }
            string username = "i" + imei, password = Guid.NewGuid().ToString("N");
            await QueueDAL.updateMqttUser(new DeviceEntity() { clientid = username, username = username, password = password, imei = imei, imsi = imsi, iccid = iccid });

            return new { clientid = username, username = username, password = password };
        }

        public static async Task<object> auth(string clientid, string username, string password)
        {
            var mqtt = await QueueDAL.queryMqttUser(username);
            if (mqtt == null || mqtt.password != password || clientid != username || !Regex.IsMatch(clientid, @"^i\d+$"))
            {
                logger.Debug($"mqtt auth refused, clientid:{clientid} username:{username} password:{password}");
                return new
                {
                    result = "deny"
                };
            }
            return new
            {
                result = "allow"
            };
        }

        public static async Task result(string clientid, string topic, string payload)
        {
            await DapperManager.Redis.PublishAsync(topic, payload);
        }

        public static async Task update(string clientid)
        {
            try
            {
                string rpath = "convert.mqtt[?(@.pid=='')]";
                var mqtt = await QueueDAL.queryMqtt(clientid);
                if (mqtt == null || string.IsNullOrEmpty(mqtt.min_version))
                {
                    return;
                }
                string file = $"{JsonHelper.GetJsonValue<string>(rpath + ".filepath")}/{mqtt.product}/v{mqtt.min_version}{(!string.IsNullOrEmpty(mqtt.branchv) ? "_" + mqtt.branchv : "")}";
                if (new Version(mqtt.version) < new Version(mqtt.min_version) && Directory.Exists(file))
                {
                    await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/v5/publish")
                        .WithBasicAuth(JsonHelper.GetJsonValue<string>(rpath + ".username"), JsonHelper.GetJsonValue<string>(rpath + ".password"))
                        .PostJsonAsync(new
                        {
                            topic =  new Version(mqtt.version) >= new Version(1, 0, 9) ?  $"device/{clientid}/config/ufota" : $"{clientid}/config/ufota",
                            payload = JsonConvert.SerializeObject(new
                            {
                                ekey = clientid,
                                burl = $"{JsonHelper.GetJsonValue<string>(rpath + ".fileurl")}/{mqtt.product}/v{mqtt.min_version}{(!string.IsNullOrEmpty(mqtt.branchv) ? "_" + mqtt.branchv : "")}",
                                file = Directory.GetFiles(file).Select(rw => Path.GetFileName(rw))
                            })
                        });
                }
            }
            catch { }
        }

        public static async Task config(string clientid, string topic, string payload)
        {
            if (await QueueDAL.existMqttUser(clientid) != null)
            {
                //核心微服务已存储，仅保留自动升级
                //JObject pjson = JObject.Parse(payload);
                //await QueueDAL.updateMqttConfig(new DeviceEntity() { clientid = clientid, product = pjson["product"]?.Value<string>() ?? "", version = pjson["version"]?.Value<string>() ?? "", branchv = pjson["branchv"]?.Value<string>() ?? "", attributes = payload });
                //if (pjson["device"]?["imei"] != null && pjson["device"]?["imsi"] != null && pjson["device"]?["iccid"] != null)
                //{
                //    await QueueDAL.updateMqttUserConfig(new DeviceEntity() { username = clientid, imei = pjson["device"]["imei"].Value<string>(), imsi = pjson["device"]["imsi"].Value<string>(), iccid = pjson["device"]["iccid"].Value<string>() });
                //}
                await update(clientid);
            }
        }

        public static async Task report(string clientid, string topic, string payload)
        {
            if (await QueueDAL.existMqttUser(clientid) != null)
            {
                JObject pjson = JObject.Parse(payload);
                await QueueDAL.updateMqttReport(new DeviceEntity() { clientid = clientid, telemetry = payload });
            }
        }

        public static async Task connected(string clientid, string peername)
        {
            if (await QueueDAL.existMqttUser(clientid) != null)
            {
                await QueueDAL.updateMqttConnect(new DeviceEntity() { clientid = clientid, connect_time = DateTime.Now });
            }
        }

        public static async Task disconnected(string clientid, string reason)
        {
            if (await QueueDAL.existMqttUser(clientid) != null)
            {
                await QueueDAL.updateMqttDisconnect(new DeviceEntity() { clientid = clientid, disconnect_reason = reason, disconnect_time = DateTime.Now });
            }
        }
    }
}
