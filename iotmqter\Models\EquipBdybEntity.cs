using SqlSugar;

namespace iotmqter.Models
{
    /// <summary>
    /// 北电云表设备表实体
    /// </summary>
    [SugarTable("tb_equip_bdyb")]
    [SugarIndex("idx_equip_bdyb_devno", nameof(DevNo), OrderByType.Asc, true)]
    public class EquipBdybEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "sysid")]
        public int SysId { get; set; }

        /// <summary>
        /// 设备编号，唯一索引
        /// </summary>
        [SugarColumn(ColumnName = "devno", IsNullable = false, Length = 50, DefaultValue = "")]
        public string DevNo { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型
        /// </summary>
        [SugarColumn(ColumnName = "ptype", IsNullable = true, Length = 100, DefaultValue = "")]
        public string? PType { get; set; } = string.Empty;

        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(ColumnName = "brand", IsNullable = true, Length = 10, DefaultValue = "GL")]
        public string? Brand { get; set; } = "GL";

        /// <summary>
        /// 推送Unilink
        /// </summary>
        [SugarColumn(ColumnName = "unilink", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Unilink { get; set; } = string.Empty;

        /// <summary>
        /// 变量
        /// </summary>
        [SugarColumn(ColumnName = "telemetry", IsNullable = true, ColumnDataType = "TEXT")]
        public string? Telemetry { get; set; }

        /// <summary>
        /// 版本属性
        /// </summary>
        [SugarColumn(ColumnName = "attributes", IsNullable = true, ColumnDataType = "TEXT")]
        public string? Attributes { get; set; }

        /// <summary>
        /// 事件，如alarm:[{"temph":"up", ts:0}]
        /// </summary>
        [SugarColumn(ColumnName = "events", IsNullable = true, ColumnDataType = "TEXT")]
        public string? Events { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "update_time", IsNullable = true, DefaultValue = "now() ON UPDATE CURRENT_TIMESTAMP")]
        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
} 