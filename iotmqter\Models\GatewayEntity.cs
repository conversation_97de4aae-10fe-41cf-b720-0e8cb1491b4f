using SqlSugar;

namespace iotmqter.Models
{
    /// <summary>
    /// 网关表实体
    /// </summary>
    [SugarTable("tb_gateway")]
    [SugarIndex("idx_gateway_cpuid", "cpud", OrderByType.Asc, true)]
    public class GatewayEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "sysid")]
        public int SysId { get; set; }

        /// <summary>
        /// CPU ID，唯一索引
        /// </summary>
        [SugarColumn(ColumnName = "cpud", IsNullable = false, Length = 50, DefaultValue = "")]
        public string CpuId { get; set; } = string.Empty;

        /// <summary>
        /// UUID
        /// </summary>
        [SugarColumn(ColumnName = "uuid", IsNullable = true, Length = 50, DefaultValue = "")]
        public string? Uuid { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time", IsNullable = true, DefaultValue = "now()")]
        public DateTime? CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "update_time", IsNullable = true, DefaultValue = "now() ON UPDATE CURRENT_TIMESTAMP")]
        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }
} 