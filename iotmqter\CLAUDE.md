# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a .NET 8 IoT MQTT message forwarding service that processes and transforms MQTT messages between devices and application systems. It replaces EMQX rule engine functionality with a dedicated service for high-performance message routing and device management.

## Build and Development Commands

### Build and Run
```bash
# Build the project
dotnet build

# Run in development mode
dotnet run

# Build for production
dotnet publish -c Release -o ./publish /p:UseAppHost=false

# Run tests (if any exist)
dotnet test
```

### Docker Deployment
```bash
# Build Docker image
docker build -t iotmqter .

# Run with docker-compose
docker-compose up -d

# Stop services
docker-compose down
```

## Architecture and Key Components

### Core Architecture
- **Minimal API Design**: Uses .NET 8 Minimal API instead of Controllers for better performance
- **Singleton Services**: All core services (MQTT, Transform, GrpIdMapper) use Singleton lifetime for optimal performance
- **High-Concurrency Focus**: Designed for extreme high-throughput MQTT message processing

### Key Services
- **MqttService** (`Services/MqttService.cs`): MQTT client management with auto-reconnect and health monitoring
- **TransformService** (`Services/TransformService.cs`): Message transformation logic for different device protocols
- **GrpIdMapper** (`Services/GrpIdMapper.cs`): Thread-safe mapping service for optimizing device group IDs
- **RedisService** (`Services/RedisService.cs`): Redis caching for message storage and token management

### Database Layer
- **ORM**: SqlSugar with MySQL 8.0
- **Entities**: All database entities use `Entity` suffix (e.g., `DeviceEntity`, `UserEntity`)
- **No Repository Pattern**: Database operations are performed directly in API endpoints for simplicity
- **Connection**: Uses environment variable `DB_CONNECTION_STRING`

### MQTT Message Processing
The service implements 11 core message routing rules:

1. **unilink_subscribe/publish**: Device ↔ Application message routing via unilink protocol
2. **dispatch_subscribe/publish**: Device ↔ Application routing with group ID optimization
3. **Device config/report**: Configuration and telemetry data storage
4. **Redis caching**: Short-term message caching for response retrieval
5. **BeiDian Cloud Table**: External API integration for specialized devices
6. **Connection events**: Real-time device online/offline status tracking

## Configuration Management

### Environment Variables (Required)
```bash
# MQTT Configuration
MQTT_BROKER_ADDR=host.docker.internal
MQTT_BROKER_PORT=1883
MQTT_USERNAME=admin
MQTT_PASSWORD=password

# Database
DB_CONNECTION_STRING=server=mysql;port=3306;database=iot_mqtt;user=root;password=yourpassword;CharSet=utf8;

# Redis
REDIS_CONNECTION_STRING=127.0.0.1:6379,defaultDatabase=0

# JWT Authentication (Required - must be 16+ characters)
JWT_KEY=your_secure_jwt_key_at_least_16_chars
JWT_ISSUER=iotmqter
JWT_AUDIENCE=iotmqter-api

# BeiDian Cloud Table API
BDYB_USERNAME=username
BDYB_KEY=api_key
BDYB_URL=https://api.example.com

# EMQX API Integration
EMQX_API_URL=http://emqx:18083
EMQX_API_USERNAME=admin
EMQX_API_PASSWORD=public
```

## API Endpoints

### Health and Status
- `GET /health` - Health check endpoint
- `GET /status` - Basic service status
- `GET /mqtt/status` - MQTT connection details
- `GET /mapper/status` - GrpID mapping statistics

### Authentication
- `POST /oauth/login` - JWT token generation
- `GET /oauth/bdyb` - BeiDian Cloud Table token (with Redis caching)

### Device Management
- `GET /core/device_license` - Generate device credentials
- `GET /core/gateway_license` - Generate gateway authorization file
- `POST /mqtt/auth` - MQTT client authentication
- `GET /mqtt/update` - Device firmware update handler

### MQTT Integration
- `POST /bdyb/beidian-response` - BeiDian Cloud Table response handler

## Development Guidelines

### Code Structure
- **Program.cs**: Main application entry with all API endpoints defined inline
- **Models/**: Data models and configuration classes
- **Services/**: Business logic services with interfaces
- **No Controllers**: Uses Minimal API pattern exclusively

### Performance Principles
- All I/O operations are async
- Services use Singleton lifetime for maximum performance
- Thread-safe implementations for concurrent access
- Optimized HttpClient with SocketsHttpHandler
- No resource limits in production deployment

### Database Best Practices
- Use SqlSugar attributes for column mapping: `[SugarColumn("column_name")]`
- Index declaration: `[SugarIndex("idx_{table}_field", nameof(Field), OrderByType.Asc, true)]`
- Entities must use `Entity` suffix
- Batch operations preferred over individual database calls

### Error Handling
- Comprehensive logging with timestamps and context
- Graceful degradation for external service failures
- Health checks for critical components (MQTT, database)

### Dependencies
- **MQTTnet 4.3.1.873**: MQTT client library
- **SqlSugarCore ***********: ORM framework
- **StackExchange.Redis 2.8.41**: Redis client
- **MySql.Data 8.2.0**: MySQL connector

## Important Implementation Notes

### GrpId Mapping Optimization
The `GrpIdMapper` service provides thread-safe mapping between long group IDs and short aliases to optimize MQTT message size and device bandwidth usage.

### MQTT Connection Reliability
- Exponential backoff reconnection strategy
- CleanSession=false for session persistence
- QoS 1 for message delivery guarantee
- Connection health monitoring

### BeiDian Cloud Table Integration
- Distributed locking for token requests
- Token caching with automatic expiration
- Command ID to task ID mapping for response correlation

### Security
- RSA encryption for gateway licenses
- JWT authentication with configurable secrets
- Environment-based configuration (no hardcoded credentials)
- Input validation for all API endpoints

When working with this codebase, prioritize performance and simplicity. The architecture is designed for high-throughput message processing with minimal latency.