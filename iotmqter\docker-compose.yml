version: '3.8'

services:
  iotmqter:
    build:
      context: .
      dockerfile: Dockerfile
    image: iotmqter:latest
    container_name: iotmqter
    restart: unless-stopped
    ports:
      - "8005:80"
    env_file:
      - .env
    environment:
      # System Configuration
      - ASPNETCORE_ENVIRONMENT=Production
      - TZ=Asia/Shanghai
    
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:80/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3 