# 用户特定的文件
*.suo
*.user
*.userosscache
*.sln.docstates
.vs/

# 编译结果
bin/
obj/
publish/
dist/
build/
Release/
Debug/
*.dll
*.exe
*.pdb

# Visual Studio临时文件
.vs/
.vscode/
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# 日志文件
logs/
*.log

# Docker
.env

# 系统文件
.DS_Store
Thumbs.db

# NuGet
packages/
*.nupkg
*.snupkg
# 除非你想明确包含它们
# !packages/repositories.config
# !NuGet.Config
*.nuget.props
*.nuget.targets

# 测试结果
TestResults/
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# 其他
node_modules/
.idea/
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user 