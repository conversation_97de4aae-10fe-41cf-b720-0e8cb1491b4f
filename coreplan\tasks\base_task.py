"""
任务基类 - 提供通用的任务执行框架
所有具体任务模块都应该继承此基类
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any


class BaseTask(ABC):
    """任务基类"""
    
    def __init__(self, task_name: str):
        self.task_name = task_name
        self.logger = None
        
    def execute(self, parameters: Dict[str, Any], logger: logging.Logger) -> bool:
        """
        任务执行入口 - 提供统一的错误处理和日志记录
        
        Args:
            parameters: 任务参数
            logger: 日志记录器
            
        Returns:
            bool: 执行是否成功
        """
        self.logger = logger
        
        try:
            self.logger.info(f"开始执行任务: {self.task_name}")
            
            # 验证参数
            if not self.validate_parameters(parameters):
                return False
                
            # 执行具体任务逻辑
            result = self.run_task(parameters)
            
            if result:
                self.logger.info(f"任务执行成功: {self.task_name}")
            else:
                self.logger.error(f"任务执行失败: {self.task_name}")
                
            return result
            
        except Exception as e:
            self.logger.error(f"任务 {self.task_name} 执行异常: {e}")
            return False
            
    @abstractmethod
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """
        具体任务实现 - 子类必须实现此方法
        
        Args:
            parameters: 任务参数
            
        Returns:
            bool: 执行是否成功
        """
        pass
        
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        验证任务参数 - 子类可以重写此方法
        
        Args:
            parameters: 任务参数
            
        Returns:
            bool: 参数是否有效
        """
        return True
        
    def get_required_parameters(self) -> list:
        """
        获取必需的参数列表 - 子类可以重写此方法
        
        Returns:
            list: 必需参数名称列表
        """
        return []
        
    def send_notification(self, message: str, notification_config: Dict[str, Any] = None):
        """
        发送通知 - 提供统一的通知机制
        
        Args:
            message: 通知消息
            notification_config: 通知配置
        """
        if not notification_config:
            return
            
        self.logger.info(f"通知消息: {message}")
        
        # 发送到webhook（如果配置了）
        webhook_url = notification_config.get('webhook')
        if webhook_url:
            self._send_webhook_notification(webhook_url, message)
            
        # 发送邮件（如果配置了）
        email = notification_config.get('email')
        if email:
            self._send_email_notification(email, message)
            
    def _send_webhook_notification(self, webhook_url: str, message: str):
        """发送webhook通知"""
        try:
            import requests
            payload = {
                'type': 'task_notification',
                'task_name': self.task_name,
                'message': message,
                'timestamp': self._get_timestamp()
            }
            response = requests.post(webhook_url, json=payload, timeout=10)
            if response.status_code == 200:
                self.logger.info("Webhook通知发送成功")
            else:
                self.logger.error(f"Webhook通知发送失败: {response.status_code}")
        except Exception as e:
            self.logger.error(f"发送webhook通知失败: {e}")
            
    def _send_email_notification(self, email: str, message: str):
        """发送邮件通知（示例实现）"""
        try:
            self.logger.info(f"模拟发送邮件到 {email}: {message}")
        except Exception as e:
            self.logger.error(f"发送邮件通知失败: {e}")
            
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
        
    def save_result(self, result_data: Dict[str, Any], file_path: str, max_records: int = 100):
        """
        保存任务结果到文件 - 提供统一的结果保存机制
        
        Args:
            result_data: 结果数据
            file_path: 保存文件路径
            max_records: 最大记录数量
        """
        try:
            import json
            import os
            
            # 创建目录
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 读取现有数据
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    all_results = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                all_results = []
                
            # 添加新数据
            result_data['timestamp'] = self._get_timestamp()
            result_data['task_name'] = self.task_name
            all_results.append(result_data)
            
            # 保留最新记录
            if len(all_results) > max_records:
                all_results = all_results[-max_records:]
                
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
                
            self.logger.debug(f"任务结果已保存到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存任务结果失败: {e}")