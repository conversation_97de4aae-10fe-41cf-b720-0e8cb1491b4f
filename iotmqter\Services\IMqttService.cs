namespace iotmqter.Services
{
    /// <summary>
    /// MQTT服务接口，用于外部调用MQTT服务
    /// </summary>
    public interface IMqttService
    {
        /// <summary>
        /// 启动MQTT服务，建立连接并订阅主题
        /// </summary>
        Task StartAsync();
        
        /// <summary>
        /// 获取MQTT连接状态
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 获取当前重连尝试次数
        /// </summary>
        int ReconnectAttempts { get; }
        
        /// <summary>
        /// 消息接收事件，参数分别为：主题、消息内容、客户端ID
        /// </summary>
        event Func<string, string, Task>? MessageReceived;
        
        /// <summary>
        /// 发布MQTT消息
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息内容</param>
        /// <returns>发布任务</returns>
        Task PublishMessageAsync(string topic, string payload);
        
        /// <summary>
        /// 发布MQTT消息（带QoS和retain参数）
        /// </summary>
        /// <param name="topic">主题</param>
        /// <param name="payload">消息内容</param>
        /// <param name="qos">服务质量等级 (0, 1, 或 2)</param>
        /// <param name="retain">是否保留消息</param>
        /// <returns>发布任务</returns>
        Task PublishMessageAsync(string topic, string payload, int qos, bool retain = false);
        
        /// <summary>
        /// 订阅MQTT主题
        /// </summary>
        /// <param name="topic">要订阅的主题</param>
        /// <param name="qos">服务质量等级 (0, 1, 或 2)</param>
        /// <returns>订阅任务</returns>
        Task SubscribeAsync(string topic, int qos = 0);
    }
} 