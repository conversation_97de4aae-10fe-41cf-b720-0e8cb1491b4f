#!/usr/bin/env python3
"""
定时任务调度器 - 核心入口执行器
负责读取YAML配置并调度各个任务模块的执行
"""

import os
import sys
import time
import signal
import importlib
import logging
import logging.handlers
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Optional

import yaml
from croniter import croniter


def parse_size(size_str: str) -> int:
    """解析大小字符串为字节数"""
    size_str = size_str.upper().strip()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


class TaskScheduler:
    """核心任务调度器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化调度器"""
        self.config_path = config_path
        self.config = {}
        self.running = False
        self.tasks = {}
        self.executor = None
        self.logger = None
        self.last_check = datetime.now()
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """处理系统信号，优雅关闭"""
        self.logger.info(f"收到信号 {signum}，准备关闭调度器...")
        self.stop()
        
    def load_config(self):
        """加载并验证YAML配置文件"""
        try:
            if not os.path.exists(self.config_path):
                print(f"错误: 配置文件不存在 - {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
                
            # 验证配置
            if not self._validate_config():
                return False
                
            print(f"配置文件加载成功: {self.config_path}")
            return True
        except yaml.YAMLError as e:
            print(f"错误: YAML格式错误 - {e}")
            return False
        except Exception as e:
            print(f"错误: 加载配置失败 - {e}")
            return False
            
    def _validate_config(self) -> bool:
        """验证配置文件格式"""
        required_sections = ['global', 'tasks']
        for section in required_sections:
            if section not in self.config:
                print(f"配置文件缺少必需的section: {section}")
                return False
                
        tasks = self.config.get('tasks', {})
        for task_name, task_config in tasks.items():
            if not isinstance(task_config, dict):
                print(f"任务 {task_name} 配置格式错误")
                return False
            if 'module' not in task_config:
                print(f"任务 {task_name} 缺少module配置")
                return False
            if 'schedule' not in task_config:
                print(f"任务 {task_name} 缺少schedule配置")
                return False
        return True
            
    def setup_logging(self):
        """设置日志系统"""
        global_config = self.config.get('global', {})
        log_level = global_config.get('log_level', 'INFO')
        log_file = global_config.get('log_file', 'logs/scheduler.log')
        max_size = global_config.get('log_max_size', '10MB')
        backup_count = global_config.get('log_backup_count', 5)
        
        # 创建日志目录
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        # 创建日志记录器
        self.logger = logging.getLogger('TaskScheduler')
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
            
        # 日志格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 文件处理器（带轮转）
        try:
            size_bytes = parse_size(max_size)
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=size_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        except Exception as e:
            print(f"创建文件日志处理器失败: {e}")
            
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        self.logger.info("日志系统初始化完成")
        
    def load_tasks(self):
        """加载和验证任务模块"""
        tasks_config = self.config.get('tasks', {})
        
        for task_name, task_config in tasks_config.items():
            if not task_config.get('enabled', False):
                self.logger.info(f"任务 {task_name} 已禁用，跳过加载")
                continue
                
            try:
                # 动态导入任务模块
                module_name = task_config.get('module')
                if not module_name:
                    self.logger.error(f"任务 {task_name} 缺少module配置")
                    continue
                    
                module = importlib.import_module(module_name)
                
                # 验证模块是否有required方法
                if not hasattr(module, 'execute'):
                    self.logger.error(f"任务模块 {module_name} 缺少execute方法")
                    continue
                    
                # 创建任务信息
                task_info = {
                    'name': task_name,
                    'module': module,
                    'config': task_config,
                    'last_run': None,
                    'next_run': self._calculate_next_run(task_config),
                    'running': False
                }
                
                self.tasks[task_name] = task_info
                self.logger.info(f"任务 {task_name} 加载成功，下次执行: {task_info['next_run']}")
                
            except ImportError as e:
                self.logger.error(f"任务 {task_name} 模块导入失败: {e}")
            except Exception as e:
                self.logger.error(f"任务 {task_name} 加载失败: {e}")
                
    def _calculate_next_run(self, task_config: Dict[str, Any]) -> datetime:
        """计算任务下次执行时间"""
        schedule = task_config.get('schedule', {})
        schedule_type = schedule.get('type', 'interval')
        now = datetime.now()
        
        if schedule_type == 'interval':
            interval = schedule.get('interval', 3600)  # 默认1小时
            return now + timedelta(seconds=interval)
        elif schedule_type == 'cron':
            cron_expr = schedule.get('expression', '0 0 * * * *')  # 默认每小时
            cron = croniter(cron_expr, now)
            return cron.get_next(datetime)
        else:
            self.logger.error(f"不支持的调度类型: {schedule_type}")
            return now + timedelta(hours=1)
            
    def _update_next_run(self, task_name: str):
        """更新任务下次执行时间"""
        if task_name in self.tasks:
            task_info = self.tasks[task_name]
            task_info['next_run'] = self._calculate_next_run(task_info['config'])
            task_info['last_run'] = datetime.now()
            
    def execute_task(self, task_name: str) -> bool:
        """执行单个任务"""
        if task_name not in self.tasks:
            self.logger.error(f"任务不存在: {task_name}")
            return False
            
        task_info = self.tasks[task_name]
        
        if task_info['running']:
            self.logger.warning(f"任务 {task_name} 正在运行中，跳过本次执行")
            return False
            
        try:
            task_info['running'] = True
            self.logger.info(f"开始执行任务: {task_name}")
            
            # 执行任务
            module = task_info['module']
            parameters = task_info['config'].get('parameters', {})
            
            start_time = time.time()
            result = module.execute(parameters, self.logger)
            execution_time = time.time() - start_time
            
            self.logger.info(f"任务 {task_name} 执行完成，耗时: {execution_time:.2f}秒")
            
            # 更新下次执行时间
            self._update_next_run(task_name)
            
            return True
            
        except Exception as e:
            self.logger.error(f"任务 {task_name} 执行失败: {e}")
            return False
        finally:
            task_info['running'] = False
            
    def check_and_run_tasks(self):
        """检查并运行到期的任务"""
        now = datetime.now()
        pending_tasks = []
        
        for task_name, task_info in self.tasks.items():
            if (not task_info['running'] and 
                task_info['next_run'] and 
                now >= task_info['next_run']):
                pending_tasks.append(task_name)
                
        if pending_tasks:
            self.logger.info(f"发现 {len(pending_tasks)} 个待执行任务: {pending_tasks}")
            
            # 提交任务到线程池
            futures = []
            for task_name in pending_tasks:
                future = self.executor.submit(self.execute_task, task_name)
                futures.append((task_name, future))
                
            # 等待任务完成（非阻塞）
            for task_name, future in futures:
                if future.done():
                    try:
                        result = future.result()
                        if result:
                            self.logger.debug(f"任务 {task_name} 执行成功")
                    except Exception as e:
                        self.logger.error(f"任务 {task_name} 执行异常: {e}")
                        
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        status = {
            'running': self.running,
            'total_tasks': len(self.tasks),
            'enabled_tasks': len([t for t in self.tasks.values() if t['config'].get('enabled', False)]),
            'running_tasks': len([t for t in self.tasks.values() if t['running']]),
            'last_check': self.last_check.isoformat(),
            'tasks': {}
        }
        
        for task_name, task_info in self.tasks.items():
            status['tasks'][task_name] = {
                'enabled': task_info['config'].get('enabled', False),
                'running': task_info['running'],
                'last_run': task_info['last_run'].isoformat() if task_info['last_run'] else None,
                'next_run': task_info['next_run'].isoformat() if task_info['next_run'] else None
            }
            
        return status
        
    def start(self):
        """启动调度器"""
        if not self.load_config():
            return False
            
        self.setup_logging()
        self.load_tasks()
        
        if not self.tasks:
            self.logger.warning("没有可用的任务，调度器退出")
            return False
            
        # 创建线程池
        global_config = self.config.get('global', {})
        max_workers = global_config.get('max_concurrent_tasks', 5)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 启动主循环
        self.running = True
        check_interval = global_config.get('check_interval', 60)
        
        self.logger.info(f"任务调度器启动，共加载 {len(self.tasks)} 个任务")
        
        try:
            while self.running:
                self.last_check = datetime.now()
                self.check_and_run_tasks()
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭调度器...")
        finally:
            self.stop()
            
        return True
        
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.executor:
            self.logger.info("等待正在执行的任务完成...")
            self.executor.shutdown(wait=True)
        self.logger.info("任务调度器已停止")


def main():
    """主函数"""
    config_file = "config.yaml"
    
    # 支持命令行参数指定配置文件
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
        
    scheduler = TaskScheduler(config_file)
    
    try:
        if scheduler.start():
            print("调度器启动成功")
        else:
            print("调度器启动失败")
            sys.exit(1)
    except Exception as e:
        print(f"调度器运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()