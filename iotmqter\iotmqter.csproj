﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <PublishAot>false</PublishAot>
    <RootNamespace>iotmqter</RootNamespace>
    <Version>1.0.4</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Evlib.Core" Version="2.0.4" />
    <PackageReference Include="MQTTnet" Version="4.3.1.873" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.137" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.9.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <None Update="docker-compose.yml">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
