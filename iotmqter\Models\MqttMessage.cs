using System.Text.Json.Serialization;

namespace iotmqter.Models
{
    // 通用MQTT消息基类
    public class MqttMessage
    {
        public string? ClientId { get; set; }
        public string? Topic { get; set; }
        public string? Payload { get; set; }
    }

    // Unilink请求消息
    public class UnilinkRequestMessage
    {
        [JsonPropertyName("taskid")]
        public string? TaskId { get; set; }
        
        [JsonPropertyName("method")]
        public string Method { get; set; } = "setcommand";
        
        [JsonPropertyName("params")]
        public UnilinkParams? Params { get; set; }
    }

    public class UnilinkParams
    {
        [JsonPropertyName("cmd")]
        public string? Cmd { get; set; }
    }

    // Unilink响应消息
    public class UnilinkResponseMessage
    {
        [JsonPropertyName("taskid")]
        public string? TaskId { get; set; }
        
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        [JsonPropertyName("method")]
        public string Method { get; set; } = "setcommand";
        
        [JsonPropertyName("result")]
        public string? Result { get; set; }
    }

    // 设备请求消息
    public class DeviceRequestMessage
    {
        [JsonPropertyName("uuid")]
        public string? Uuid { get; set; }
        
        [JsonPropertyName("data")]
        public string? Data { get; set; }
    }

    // 设备响应消息
    public class DeviceResponseMessage
    {
        [JsonPropertyName("uuid")]
        public string? Uuid { get; set; }
        
        [JsonPropertyName("status")]
        public int Status { get; set; }
        
        [JsonPropertyName("result")]
        public string? Result { get; set; }
    }
} 