"""
数据备份任务模块
定期备份指定目录或数据库
"""

import os
import shutil
import zipfile
from datetime import datetime, timedelta
from typing import Dict, Any

from .base_task import BaseTask


class DataBackupTask(BaseTask):
    """数据备份任务"""
    
    def __init__(self):
        super().__init__("数据备份")
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """执行数据备份任务"""
        backup_path = parameters.get('backup_path', '/backup')
        retention_days = parameters.get('retention_days', 7)
        source_paths = parameters.get('source_paths', ['/data'])
        
        # 创建备份目录
        if not os.path.exists(backup_path):
            os.makedirs(backup_path)
            self.logger.info(f"创建备份目录: {backup_path}")
            
        # 生成备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{timestamp}.zip"
        backup_filepath = os.path.join(backup_path, backup_filename)
        
        # 创建压缩备份
        with zipfile.ZipFile(backup_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for source_path in source_paths:
                if os.path.exists(source_path):
                    if os.path.isfile(source_path):
                        # 备份单个文件
                        zipf.write(source_path, os.path.basename(source_path))
                        self.logger.info(f"备份文件: {source_path}")
                    elif os.path.isdir(source_path):
                        # 备份目录
                        for root, dirs, files in os.walk(source_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arc_path = os.path.relpath(file_path, os.path.dirname(source_path))
                                zipf.write(file_path, arc_path)
                        self.logger.info(f"备份目录: {source_path}")
                else:
                    self.logger.warning(f"源路径不存在: {source_path}")
                    
        self.logger.info(f"备份完成: {backup_filepath}")
        
        # 清理过期备份
        self._cleanup_old_backups(backup_path, retention_days)
        
        return True
    def _cleanup_old_backups(self, backup_path: str, retention_days: int):
        """清理过期的备份文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            deleted_count = 0
            
            for filename in os.listdir(backup_path):
                if filename.startswith('backup_') and filename.endswith('.zip'):
                    filepath = os.path.join(backup_path, filename)
                    
                    # 获取文件修改时间
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_mtime < cutoff_date:
                        os.remove(filepath)
                        deleted_count += 1
                        self.logger.info(f"删除过期备份: {filename}")
                        
            if deleted_count > 0:
                self.logger.info(f"清理完成，删除了 {deleted_count} 个过期备份文件")
            else:
                self.logger.info("没有需要清理的过期备份文件")
                
        except Exception as e:
            self.logger.error(f"清理过期备份失败: {e}")
            

# 为了向后兼容，保留原有的execute函数接口
def execute(parameters: Dict[str, Any], logger) -> bool:
    """向后兼容的执行函数"""
    task = DataBackupTask()
    return task.execute(parameters, logger)