# IoT MQTT 消息转发服务

本服务实现了与EMQX规则引擎相同的四个消息重发布规则，用于IoT设备与应用系统之间的消息转换。

## 功能说明

服务实现了以下消息转发规则：

### 1. unilink_subscribe
- 监听主题：`device/+/respone/relay`（从设备端接收的响应）
- 处理逻辑：
  - 提取设备类型（type）、UUID和设备IMEI
  - 将设备响应转换为应用系统所需的消息格式
- 发布主题：`v1/unilink/${type}/${imei}/rpc/response`（发送给应用系统）

### 2. unilink_publish
- 监听主题：`v1/unilink/+/+/rpc/request`（从应用系统接收的请求）
- 处理逻辑：
  - 提取设备类型、IMEI和指令内容
  - 将应用系统请求转换为设备所需的消息格式
  - 如消息中无taskid，则自动生成唯一ID
- 发布主题：`device/i${imei}/request/relay`（发送给设备）

### 3. dispatch_subscribe
- 监听主题：`device/+/respone/relay`（从设备端接收的响应）
- 处理逻辑：
  - 提取组ID别名（grpid_alias）、UUID和设备IMEI
  - 将短数字形式的grpid_alias转换回原始长字符串grpid
  - 将设备响应转换为应用系统所需的消息格式
- 发布主题：`v1/dispatch/${grpid}/${imei}/rpc/response`（发送给应用系统）

### 4. dispatch_publish
- 监听主题：`v1/dispatch/+/+/rpc/request`（从应用系统接收的请求）
- 处理逻辑：
  - 提取组ID（grpid）、IMEI和指令内容
  - 将长字符串形式的grpid转换为短数字形式的grpid_alias
  - 将应用系统请求转换为设备所需的消息格式
  - 如消息中无taskid，则自动生成唯一ID
- 发布主题：`device/i${imei}/request/relay`（发送给设备）

### 5. 设备配置消息处理
- 监听主题：`device/config/#`（设备配置消息）
- 处理逻辑：
  - 从主题中提取客户端ID（格式：device/config/${clientid}）
  - 将消息内容更新到tb_device表的attributes字段
- 功能：存储设备配置信息，用于设备管理和状态监控

### 6. 设备上报消息处理
- 监听主题：`device/report/#`（设备上报消息）
- 处理逻辑：
  - 从主题中提取客户端ID（格式：device/report/${clientid}）
  - 将消息内容更新到tb_device表的telemetry字段
- 功能：存储设备遥测数据，用于数据分析和设备监控

### 7. 设备响应消息Redis缓存和发布
- 监听主题：`device/+/respone/#`（所有设备响应主题）
- 处理逻辑：
  - 将消息内容以主题为键写入Redis
  - 键格式：`guid.{主题}`
  - 同时将消息发布到Redis通道，通道名为主题
  - 支持Redis发布/订阅(Pub/Sub)机制获取实时响应数据
- 功能：短时缓存设备响应消息，用于消息处理和查询，同时支持实时通知

### 8. 北电云表设备管理
- 数据库表：`tb_equip_bdyb`
- 功能：存储北电云表设备信息，包括设备编号、类型、变量、属性及事件等

### 9. 北电云表请求处理
- 监听主题：`v1/vftkt/+/rpc/request`
- 处理逻辑：
  - 提取unilink值和taskid字段
  - 获取北电云表令牌（通过本地/oauth/bdyb接口）
  - 将请求转发到北电云表API（/api/device/command）
  - 从响应中提取commandId
  - 将commandId和taskid的映射关系写入Redis，有效期30秒
- 功能：处理北电云表请求消息，实现云端控制
- 关联接口：
  - `/oauth/bdyb`：获取北电云表认证令牌
  - `/bdyb/beidian-response`：处理北电云表响应

### 10. 设备连接事件处理
- 监听主题：`$SYS/brokers/+/clients/+/connected`（设备连接事件）
- 处理逻辑：
  - 从主题中提取客户端ID
  - 更新设备在线状态(is_online=1)和连接时间(connect_time)
  - 如果设备的unilink字段不为空，将消息推送到主题`v1/unilink/${unilink}/${clientid}/client_connected`
  - 如果设备的dispatch字段不为空，将消息推送到主题`v1/dispatch/${dispatch}/${clientid}/client_connected`
- 功能：实时监控设备连接状态，更新数据库记录，并通知应用系统

### 11. 设备断开连接事件处理
- 监听主题：`$SYS/brokers/+/clients/+/disconnected`（设备断开连接事件）
- 处理逻辑：
  - 从主题中提取客户端ID
  - 更新设备离线状态(is_online=0)、断开连接时间(disconnect_time)和断开原因(disconnect_reason)
  - 如果设备的unilink字段不为空，将消息推送到主题`v1/unilink/${unilink}/${clientid}/client_disconnected`
  - 如果设备的dispatch字段不为空，将消息推送到主题`v1/dispatch/${dispatch}/${clientid}/client_disconnected`
- 功能：实时监控设备断开连接状态，更新数据库记录，并通知应用系统

## 特殊功能

### GrpId映射机制

服务实现了一个线程安全的GrpId映射机制，用于解决以下问题：

1. 在dispatch_publish中，将原始长字符串grpid缓存并建立索引，使用自增值创建映射
2. 在dispatch_subscribe中，将短数字形式的grpid_alias转换回原始长字符串grpid

这个机制有以下优势：
- 降低设备端MQTT消息内容的长度，节约带宽和设备资源
- 使用ConcurrentDictionary和线程安全机制实现高并发支持
- 采用双向映射，支持快速查找
- 优化的ID溢出处理，确保ID复用时的安全性
- 避免ID冲突，保证映射的唯一性和一致性

### 高可靠MQTT连接

服务实现了高可靠的MQTT连接机制：

1. 自动重连：
   - 使用指数退避算法动态调整重连间隔，避免频繁重连
   - 无限重试策略，确保最终能够重建连接
   - 线程安全的重连控制，避免多线程同时尝试重连

2. 会话保持：
   - 使用CleanSession=false保持会话状态
   - 合理的心跳间隔配置，及时检测连接状态
   - 消息质量保证机制（QoS 1）

3. 健康检查：
   - 实时监控MQTT连接状态
   - 提供健康检查API端点

### Redis缓存与发布/订阅机制

服务实现了Redis缓存及发布/订阅(Pub/Sub)机制，用于以下功能：

1. 缓存MQTT消息：
   - 将设备响应消息缓存到Redis中，方便查询和处理
   - 使用主题直接作为键名
   - 设置短时过期，避免缓存过多数据

2. 实时消息通知：
   - 将消息发布到Redis通道，支持其他服务实时订阅消息
   - 使用主题名作为通道名称，便于识别和过滤
   - 不受缓存过期时间限制，可即时获取消息

3. 北电云表响应处理：
   - 将北电云表响应数据发布到Redis通道
   - 使用commandId作为通道名
   - 同时支持键值存储和发布/订阅两种访问方式
   - 便于应用系统以多种方式获取响应结果

## 配置说明

服务使用环境变量进行配置，以便在Docker环境中更灵活地部署：

```
# MQTT配置环境变量
MQTT_BROKER_ADDR=host.docker.internal  # MQTT服务器地址
MQTT_BROKER_PORT=1883                  # MQTT服务器端口
MQTT_USERNAME=admin                    # 认证用户名（可选）
MQTT_PASSWORD=password                 # 认证密码（可选）
MQTT_RECONNECT_DELAY=5                 # 基础重连延迟秒数（可选）

# 数据库配置环境变量
DB_CONNECTION_STRING=server=mysql;port=3306;database=iot_mqtt;user=root;password=yourpassword;CharSet=utf8;  # 数据库连接字符串

# Redis配置环境变量
REDIS_CONNECTION_STRING=127.0.0.1:6379,defaultDatabase=0  # Redis连接字符串

# JWT配置环境变量（用于API认证）
JWT_KEY=your_secure_jwt_key_at_least_16_chars          # JWT密钥（必需，至少16字符）
JWT_ISSUER=iotmqter                                    # JWT发行者（可选）
JWT_AUDIENCE=iotmqter-api                              # JWT受众（可选）

# 北电云表配置环境变量
BDYB_USERNAME=username                     # 北电云表API用户名
BDYB_KEY=api_key                           # 北电云表API密钥
BDYB_URL=https://api.example.com           # 北电云表API基础URL

# EMQX配置环境变量
EMQX_API_URL=http://emqx:18083             # EMQX API地址
EMQX_API_USERNAME=admin                    # EMQX API用户名
EMQX_API_PASSWORD=public                   # EMQX API密码
```

## API端点

服务提供以下HTTP端点用于监控和诊断：

- `/status`: 返回服务基本状态信息
- `/health`: 符合标准的健康检查端点，用于容器编排系统监控
- `/mqtt/status`: 返回MQTT客户端详细状态，包括连接状态和重连次数
- `/mapper/status`: 返回GrpID映射状态信息

### MQTT消息发布接口

#### 快捷MQTT消息发布
- 端点：`POST /mqtt/publish`
- 功能：直接向MQTT代理发布消息，支持自定义QoS和retain参数
- 请求体：
  ```json
  {
    "topic": "test/topic",
    "payload": "消息内容",
    "qos": 0,
    "retain": false
  }
  ```
- 参数说明：
  - `topic` (必需): MQTT主题，支持使用逗号分隔多个主题进行批量发布
  - `payload` (可选): 消息载荷，默认为空字符串
  - `qos` (可选): 服务质量等级，支持0、1、2，默认为0
  - `retain` (可选): 是否保留消息，默认为false
- 批量发布示例：
  ```json
  {
    "topic": "test/topic1,test/topic2,test/topic3",
    "payload": "同一消息内容将发布到多个主题",
    "qos": 1,
    "retain": false
  }
  ```
- 响应：
  ```json
  {
    "success": true,
    "message": "消息已发布"
  }
  ```
  或批量发布时：
  ```json
  {
    "success": true,
    "message": "已向3个主题发布消息"
  }
  ```

### 认证和授权接口

#### OAuth登录接口
- 端点：`POST /oauth/login`
- 功能：验证用户凭证，返回JWT令牌
- 请求体：
  ```json
  {
    "username": "app_id",
    "password": "secret_key"
  }
  ```
- 响应：
  ```json
  {
    "success": true,
    "message": "登录成功",
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expireAt": **********
  }
  ```

#### 设备授权接口
- 端点：`GET /core/device_license?imei={imei}&imsi={imsi}&iccid={iccid}&ekey={ekey}`
- 功能：为设备生成MQTT连接凭证
- 参数：
  - `imei`: 设备IMEI号
  - `imsi`: SIM卡IMSI号
  - `iccid`: SIM卡ICCID号
  - `ekey`: 应用密钥
- 响应：
  ```json
  {
    "clientid": "i123456789012345",
    "username": "i123456789012345",
    "password": "generated_password"
  }
  ```

#### 网关授权接口
- 端点：`GET /core/gateway_license?cpud={cpud}&uuid={uuid}&ekey={ekey}`
- 功能：生成网关授权文件
- 参数：
  - `cpud`: 网关CPU ID
  - `uuid`: 网关UUID
  - `ekey`: 应用密钥
- 响应：返回二进制授权文件（licx格式）

#### MQTT客户端认证接口
- 端点：`POST /mqtt/auth`
- 功能：验证MQTT客户端连接权限（供EMQX调用）
- 请求体：
  ```json
  {
    "clientId": "device_client_id",
    "username": "device_username",
    "password": "device_password"
  }
  ```
- 响应：
  ```json
  {
    "result": "allow" // 或 "deny"
  }
  ```

### 设备管理接口

#### 设备更新接口
- 端点：`GET /mqtt/update?clientid={clientid}`
- 功能：检查设备版本并推送固件更新通知
- 参数：
  - `clientid`: 设备客户端ID
- 响应：
  ```json
  {
    "success": true,
    "message": "已发送更新通知",
    "updateInfo": {
      "ekey": "device_id",
      "burl": "http://update.server.com/files/v1.0.1",
      "file": ["firmware.bin", "config.json"]
    }
  }
  ```

### EMQX规则引擎HTTP接口

服务提供以下接口用于EMQX规则引擎调用：

#### 1. 北电云表响应接口
- 端点：`POST /bdyb/beidian-response`
- 请求体：
  ```json
  {
    "commandId": "command123",
    "devNo": "device001",
    "resultParams": { ... }
  }
  ```
- 功能：
  - 接收北电云表数据返回
  - 将commandId和resultParams发布到Redis通道
  - 将整个请求体写入Redis，键为"guid.{taskId}"
  - 从Redis获取commandId对应的taskId
  - 如果找到taskId，将其添加到响应消息中（taskid字段）
  - 从请求体中获取设备编号devNo
  - 查询对应的unilink值，如不为空则将响应转发到MQTT主题`v1/vftkt/${unilink}/rpc/response`
- 响应：
  ```json
  {
    "success": true,
    "message": "响应已处理"
  }
  ```

#### 2. 北电云表令牌接口
- 端点：`GET /oauth/bdyb`
- 功能：
  - 获取北电云表认证令牌
  - 使用Redis分布式锁机制处理并发访问
  - 缓存令牌到Redis，键为"oauth.token.bdyb"，复用API返回的过期时间
  - 从环境变量获取认证参数：BDYB_USERNAME、BDYB_KEY和BDYB_URL
- 响应：
  ```json
  {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expire": **********
  }
  ```

## 架构设计

本项目采用.NET 8 Minimal API设计，高度优化的代码结构：

1. **Models层**：数据模型定义
   - `MqttConfig.cs` - MQTT配置模型
   - `MqttMessage.cs` - 消息格式模型和序列化上下文
   - `DeviceEntity.cs` - 设备数据库实体模型
   - `MqttClientEventModel.cs` - MQTT客户端事件请求模型
   - `DbConfig.cs` - 数据库配置模型
   - `RedisConfig.cs` - Redis配置模型
   - `EquipBdybEntity.cs` - 北电云表设备实体模型
   - `JwtConfig.cs` - JWT认证配置模型
   - `LoginRequestModel.cs` - 登录请求模型
   - `UpdateConfig.cs` - 设备更新配置模型

2. **Services层**：业务服务实现
   - `IMqttService.cs` - MQTT服务接口
   - `MqttService.cs` - MQTT连接和消息底层处理
   - `ITransformService.cs` - 转换服务接口
   - `TransformService.cs` - 消息转换实现
   - `GrpIdMapper.cs` - GrpID映射服务，优化的线程安全实现
   - `DbService.cs` - 数据库服务
   - `IRedisService.cs` - Redis服务接口
   - `RedisService.cs` - Redis服务实现 (使用StackExchange.Redis，支持Redis 6.0+)
   - `JwtService.cs` - JWT认证服务
   - `RSAProviderService.cs` - RSA加密服务

3. **Program.cs**: 应用程序入口和基础设置
   - 轻量级应用程序配置
   - 依赖注入设置
   - API端点定义
   - 内联数据库操作实现

## 代码简化设计

本项目采用了极简设计原则，通过以下方式简化代码：

1. **直接在API端点实现数据库操作**：
   - 避免了多余的仓储层抽象
   - 减少了代码文件数量和类的数量
   - 提高了代码的可读性和可维护性

2. **使用Minimal API**：
   - 减少了控制器和中间件的使用
   - 使代码结构更加紧凑和高效

3. **依赖注入优化**：
   - 直接注入ISqlSugarClient和IRedisService到API端点
   - 避免了多余的服务层封装

4. **JSON处理优化**：
   - 使用Newtonsoft.Json.JObject替代System.Text.Json的JsonDocument和JsonElement
   - 简化了JSON操作代码，提高了可读性
   - 更直观的属性访问方式 (json["propertyName"] 代替 TryGetProperty 和 GetString)

## 最新改进

### 1. MQTT发布接口增强
- `/mqtt/publish`接口新增QoS参数支持，可设置0、1、2三种服务质量等级
- 新增retain参数支持，允许设置消息保留标志
- 改进了MqttService实现，提供了更灵活的消息发布方法重载
- 增强了日志记录，现在会显示QoS和retain参数的值
- 新增批量发布功能，支持通过逗号分隔的多个主题同时发布相同消息
- 优化了批量发布的并行处理，使用Task.WhenAll提高性能

### 2. 安全性改进
- 移除了JWT密钥的硬编码默认值，现在必须通过环境变量配置有效密钥
- 增强了对密钥长度和有效性的验证
- 改进了RSA加密服务的错误处理

### 3. 代码优化与BUG修复
- 修复了正则表达式匹配问题，确保正确匹配主题模式
- 优化了处理响应消息的逻辑，同时支持"respone"和"response"拼写
- 改进了GrpIdMapper实现，增强了线程安全性和ID冲突处理
- 优化了消息处理逻辑，为缺少taskid的消息自动生成唯一ID

### 4. Redis功能增强
- 添加了Redis发布/订阅(Pub/Sub)功能支持
- 增加了PublishAsync方法，允许直接发布消息到Redis通道
- 改进了键值存储方法，支持更灵活的缓存策略
- 优化了消息处理流程，同时支持键值存储和发布/订阅两种模式

### 5. JSON处理改进
- 从System.Text.Json迁移到Newtonsoft.Json，提供更简洁直观的API
- 使用JObject替代JsonDocument和JsonElement，简化了代码结构
- 统一使用JsonConvert.SerializeObject/DeserializeObject，而非JsonSerializer
- 提高了代码的一致性和可读性

### 6. 性能与可靠性改进
- 改进了MQTT重连机制，优化了退避策略
- 增强了错误日志记录，便于问题定位
- 优化了Redis缓存操作的异常处理

## 部署说明

### Docker部署

项目包含Docker和docker-compose配置，支持容器化部署：

1. 使用提供的Dockerfile构建镜像:
   ```
   docker build -t iotmqter .
   ```

2. 使用docker-compose进行部署:
   ```
   docker-compose up -d
   ```

### 性能优化

服务进行了以下性能优化：

1. 使用.NET 8 Minimal API，减少中间件开销
2. 无锁算法处理并发映射，高效处理大量消息
3. 异步处理所有I/O操作，最大化系统吞吐量
4. 使用强类型序列化和AOT编译友好的代码结构
5. SqlSugar ORM高效数据库操作，支持高并发
6. 减少代码层次和抽象，降低调用开销
7. Redis高性能缓存和发布/订阅，支持多种消息处理模式
8. 优化的线程安全实现，支持高并发场景
9. 高效的JSON处理，采用Newtonsoft.Json的流畅API设计

## 订阅主题列表

服务订阅以下MQTT主题：

1. `device/+/respone/relay` - 设备响应主题
2. `v1/unilink/+/+/rpc/request` - UniLink请求
3. `v1/dispatch/+/+/rpc/request` - Dispatch请求
4. `device/config/#` - 设备配置消息
5. `device/report/#` - 设备上报消息
6. `v1/vftkt/+/rpc/request` - 北电云表请求主题
7. `$SYS/brokers/+/clients/+/connected` - 设备连接事件
8. `$SYS/brokers/+/clients/+/disconnected` - 设备断开连接事件
