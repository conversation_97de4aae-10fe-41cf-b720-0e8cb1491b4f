# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Python的定时任务执行系统，使用共享的YAML配置文件来协调多个独立的任务模块。系统设计原则为简洁性和可维护性，作为辅助服务优先考虑稳定性而非效率。

## 架构设计

### 核心组件

- **调度器核心** (`scheduler.py`): 主入口点，集成了配置加载、日志管理和任务编排功能
- **任务基类** (`tasks/base_task.py`): 提供统一的任务执行框架，包含错误处理、日志记录、参数验证和通知机制
- **任务模块** (`tasks/`): 独立的Python文件，每个实现特定功能
- **配置文件** (`config.yaml`): 单一YAML文件定义所有任务的调度参数

### 架构特点

- 已完成重构，消除过度工程化：删除了utils目录，功能直接集成到调度器核心
- 统一任务基类提供标准框架，所有任务模块都继承BaseTask
- 向后兼容：保留原有execute()函数接口
- 集中式调度和配置管理

## 开发命令

```bash
# 安装依赖
pip install -r requirements.txt

# 启动调度器
python scheduler.py

# 指定配置文件启动
python scheduler.py custom_config.yaml
```

## 配置结构

`config.yaml`包含：
- 全局设置：日志级别、检查间隔、最大并发任务数
- 任务配置：调度时间(cron/interval)、启用状态、任务参数
- 特定服务配置：MySQL数据库、EMQX API等

## 任务开发规范

### 继承BaseTask基类
所有新任务都应该继承`BaseTask`并实现：

```python
from .base_task import BaseTask

class NewTask(BaseTask):
    def __init__(self):
        super().__init__("任务名称")
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        # 实现具体任务逻辑
        return True
        
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        # 可选：实现参数验证
        return True

# 向后兼容的execute函数
def execute(parameters: Dict[str, Any], logger) -> bool:
    task = NewTask()
    return task.execute(parameters, logger)
```

### 可用的基类功能
- `self.send_notification()`: 统一通知机制(webhook/email)
- `self.save_result()`: 结果保存到JSON文件
- `self.logger`: 日志记录器
- 自动错误处理和参数验证

## 现有任务模块

- **设备时钟同步** (`device_time_sync.py`): 从MySQL查询设备，通过EMQX API发送时间同步指令
- **系统监控** (`system_monitor.py`): 监控CPU、内存、磁盘使用率，支持告警
- **API健康检查** (`api_health_check.py`): 定期检查API端点可用性
- **临时文件清理** (`cleanup_temp.py`): 清理指定目录的过期文件
- **数据备份** (`data_backup.py`): 压缩备份指定目录，清理过期备份

## 添加新任务

1. 在`tasks/`目录创建新模块文件
2. 继承`BaseTask`实现`run_task()`方法
3. 在`config.yaml`的tasks段添加配置
4. 调度器会自动发现并加载新任务