using iotmqter.Models;
using SqlSugar;
using MySql.Data.MySqlClient;
using System.Text.RegularExpressions;

namespace iotmqter.Services
{
    /// <summary>
    /// 数据库服务
    /// </summary>
    public class DbService
    {
        /// <summary>
        /// 创建SqlSugarScope（线程安全的单例客户端）
        /// </summary>
        /// <param name="dbConfig">数据库配置</param>
        /// <returns>SqlSugar客户端</returns>
        public static ISqlSugarClient CreateSqlSugarScope(DbConfig dbConfig)
        {
            // 优化连接字符串，添加连接池配置
            var connectionString = BuildOptimizedConnectionString(dbConfig.ConnectionString);
            
            return new SqlSugarScope(new ConnectionConfig()
            {
                ConfigId = "Default", // 必需：为SqlSugarScope指定配置标识
                ConnectionString = connectionString,
                DbType = DbType.MySql,
                IsAutoCloseConnection = true, // SqlSugarScope 线程安全，自动关闭连接
                InitKeyType = InitKeyType.Attribute,
                MoreSettings = new ConnMoreSettings
                {
                    IsAutoRemoveDataCache = true,
                    SqlServerCodeFirstNvarchar = true,
                    PgSqlIsAutoToLower = false,
                    DisableNvarchar = false,
                    // SqlSugarScope 特定配置
                    //EnableCheckNull = false, // 高性能模式，减少空值检查
                    EnableOracleIdentity = false
                },
                AopEvents = new AopEvents
                {
                    OnLogExecuting = (sql, parameters) =>
                    {
                        // 可以在这里记录SQL执行日志
                        // Console.WriteLine($"SQL: {sql}");
                    },
                    OnError = (exp) =>
                    {
                        // 记录SQL执行错误
                        //Console.WriteLine($"SQL Error: {exp.Message}");
                    }
                },
                ConfigureExternalServices = new ConfigureExternalServices
                {
                    // SqlSugarScope 内置线程安全机制
                }
            }, 
            db => {
                // SqlSugarScope 单例配置回调
                // 所有上下文生效的全局配置
                db.Aop.OnLogExecuting = (sql, parameters) =>
                {
                    // 可以在这里添加全局SQL日志
                };
            });
        }

        /// <summary>
        /// 验证SqlSugarScope单例状态
        /// </summary>
        /// <param name="db">数据库客户端</param>
        /// <returns>单例状态信息</returns>
        public static (bool IsSingleton, int HashCode) VerifySingletonStatus(ISqlSugarClient db)
        {
            if (db is SqlSugarScope scope)
            {
                return (true, scope.GetHashCode());
            }
            return (false, db.GetHashCode());
        }

        /// <summary>
        /// 构建优化的数据库连接字符串
        /// </summary>
        /// <param name="originalConnectionString">原始连接字符串</param>
        /// <returns>优化后的连接字符串</returns>
        private static string BuildOptimizedConnectionString(string originalConnectionString)
        {
            try
            {
                // 预处理连接字符串，修复常见的格式问题
                var preprocessedConnectionString = PreprocessConnectionString(originalConnectionString);
                
                // 解析原有连接字符串参数
                var builder = new MySqlConnectionStringBuilder(preprocessedConnectionString);
                
                // 优化连接池设置（如果没有设置）
                if (!builder.Pooling)
                {
                    builder.Pooling = true; // 启用连接池
                }
                
                // 设置连接池参数（SqlSugarScope单例模式优化）
                if (builder.MinimumPoolSize < 1)
                {
                    builder.MinimumPoolSize = 10; // 单例模式下适当提高最小连接池
                }
                if (builder.MaximumPoolSize < 10 || builder.MaximumPoolSize > 500)
                {
                    builder.MaximumPoolSize = 200; // 单例模式下适当提高连接池上限
                }
                if (builder.ConnectionLifeTime < 10)
                {
                    builder.ConnectionLifeTime = 300; // 单例模式下延长连接生命周期5分钟
                }
                if (builder.ConnectionTimeout < 15)
                {
                    builder.ConnectionTimeout = 30; // 连接超时30秒
                }
                if (builder.DefaultCommandTimeout < 15)
                {
                    builder.DefaultCommandTimeout = 60; // 命令超时60秒
                }
                
                // 设置字符集（如果没有设置）
                if (string.IsNullOrEmpty(builder.CharacterSet))
                {
                    builder.CharacterSet = "utf8mb4";
                }
                
                // 设置SSL模式（保持原有设置，如果明确设置为None则保持）
                // 不强制修改SSL设置，尊重用户配置
                
                // 启用压缩（如果没有设置）
                if (!builder.UseCompression)
                {
                    builder.UseCompression = true;
                }
                
                // 允许用户变量
                builder.AllowUserVariables = true;
                
                // 设置连接重置选项
                builder.ConnectionReset = true;
                
                // 设置重试选项
                builder.ConnectionTimeout = Math.Max(builder.ConnectionTimeout, 30);
                
                return builder.ToString();
            }
            catch
            {
                // 如果解析失败，记录错误并返回原始连接字符串
                //Console.WriteLine($"Warning: Failed to optimize connection string: {ex.Message}");
                return originalConnectionString;
            }
        }

        /// <summary>
        /// 预处理连接字符串，修复常见的格式问题
        /// </summary>
        /// <param name="connectionString">原始连接字符串</param>
        /// <returns>修复后的连接字符串</returns>
        private static string PreprocessConnectionString(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return connectionString;

            // 修复常见的参数名问题
            var result = connectionString;
            
            // 修复 sslmode 为 SslMode
            result = Regex.Replace(
                result, 
                @"\bsslmode\s*=", 
                "SslMode=", 
                RegexOptions.IgnoreCase
            );
            
            // 确保密码中包含特殊字符时被正确引用
            // 查找 password= 后面的值，如果包含逗号、分号等特殊字符且没有引号，则添加引号
            result = Regex.Replace(
                result,
                @"\bpassword\s*=\s*([^;""\s][^;""]*[,;][^;""]*(?=[;\s]|$))",
                @"password=""$1""",
                RegexOptions.IgnoreCase
            );
            
            return result;
        }

        /// <summary>
        /// 初始化数据库表结构
        /// </summary>
        /// <param name="db">数据库客户端</param>
        /// <param name="createTable">是否创建表，默认为true</param>
        /// <returns>初始化结果</returns>
        public static Task<bool> InitDatabaseAsync(ISqlSugarClient db, bool createTable = true)
        {
            try
            {
                // 使用代码优先方式，确保表结构存在并符合模型定义
                if (createTable)
                {
                    // 创建表
                    db.CodeFirst.InitTables(
                        typeof(UserEntity),
                        typeof(DeviceEntity),
                        typeof(GatewayEntity),
                        typeof(EquipBdybEntity)
                    );
                }
                
                return Task.FromResult(true);
            }
            catch (Exception)
            {
                return Task.FromResult(false);
            }
        }
    }
} 