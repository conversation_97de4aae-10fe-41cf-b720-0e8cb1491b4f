using System.Text.Json.Serialization;

namespace iotmqter.Models
{
    /// <summary>
    /// MQTT认证请求模型
    /// </summary>
    public class MqttAuthRequestModel
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string? ClientId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }
        
        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }
    }
    
    /// <summary>
    /// MQTT客户端连接事件（系统主题）
    /// </summary>
    public class MqttClientConnectedEvent
    {
        /// <summary>
        /// 客户端IP地址
        /// </summary>
        [JsonPropertyName("ipaddress")]
        public string? IpAddress { get; set; }
        
        /// <summary>
        /// 过期间隔
        /// </summary>
        [JsonPropertyName("expiry_interval")]
        public int ExpiryInterval { get; set; }
        
        /// <summary>
        /// 是否清除会话
        /// </summary>
        [JsonPropertyName("clean_start")]
        public bool CleanStart { get; set; }
        
        /// <summary>
        /// 套接字端口
        /// </summary>
        [JsonPropertyName("sockport")]
        public int SockPort { get; set; }
        
        /// <summary>
        /// 连接时间（Unix时间戳，毫秒）
        /// </summary>
        [JsonPropertyName("connected_at")]
        public long ConnectedAt { get; set; }
        
        /// <summary>
        /// 协议名称
        /// </summary>
        [JsonPropertyName("proto_name")]
        public string? ProtoName { get; set; }
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        [JsonPropertyName("clientid")]
        public string? ClientId { get; set; }
        
        /// <summary>
        /// 协议版本
        /// </summary>
        [JsonPropertyName("proto_ver")]
        public int ProtoVer { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }
        
        /// <summary>
        /// 时间戳（Unix时间戳，毫秒）
        /// </summary>
        [JsonPropertyName("ts")]
        public long Ts { get; set; }
        
        /// <summary>
        /// 协议
        /// </summary>
        [JsonPropertyName("protocol")]
        public string? Protocol { get; set; }
        
        /// <summary>
        /// 保活时间（秒）
        /// </summary>
        [JsonPropertyName("keepalive")]
        public int KeepAlive { get; set; }
    }
    
    /// <summary>
    /// MQTT客户端断开连接事件（系统主题）
    /// </summary>
    public class MqttClientDisconnectedEvent
    {
        /// <summary>
        /// 客户端IP地址
        /// </summary>
        [JsonPropertyName("ipaddress")]
        public string? IpAddress { get; set; }
        
        /// <summary>
        /// 断开连接时间（Unix时间戳，毫秒）
        /// </summary>
        [JsonPropertyName("disconnected_at")]
        public long DisconnectedAt { get; set; }
        
        /// <summary>
        /// 套接字端口
        /// </summary>
        [JsonPropertyName("sockport")]
        public int SockPort { get; set; }
        
        /// <summary>
        /// 连接时间（Unix时间戳，毫秒）
        /// </summary>
        [JsonPropertyName("connected_at")]
        public long ConnectedAt { get; set; }
        
        /// <summary>
        /// 协议名称
        /// </summary>
        [JsonPropertyName("proto_name")]
        public string? ProtoName { get; set; }
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        [JsonPropertyName("clientid")]
        public string? ClientId { get; set; }
        
        /// <summary>
        /// 协议版本
        /// </summary>
        [JsonPropertyName("proto_ver")]
        public int ProtoVer { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        [JsonPropertyName("username")]
        public string? Username { get; set; }
        
        /// <summary>
        /// 时间戳（Unix时间戳，毫秒）
        /// </summary>
        [JsonPropertyName("ts")]
        public long Ts { get; set; }
        
        /// <summary>
        /// 协议
        /// </summary>
        [JsonPropertyName("protocol")]
        public string? Protocol { get; set; }
        
        /// <summary>
        /// 断开连接原因
        /// </summary>
        [JsonPropertyName("reason")]
        public string? Reason { get; set; }
    }
    
    /// <summary>
    /// 设备响应事件消息结构
    /// </summary>
    public class DeviceResponseEventMessage
    {
        /// <summary>
        /// 响应状态
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 响应结果
        /// </summary>
        public object? Result { get; set; }
    }
} 