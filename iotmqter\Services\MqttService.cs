using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using iotmqter.Models;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;
using Microsoft.Extensions.Logging;

namespace iotmqter.Services
{
    public class MqttService : IMqttService, IDisposable
    {
        private readonly IMqttClient _mqttClient;
        private readonly MqttClientOptions _options;
        private readonly ILogger<MqttService> _logger;
        private readonly MqttConfig _mqttConfig;
        
        // 重连相关变量
        private readonly SemaphoreSlim _reconnectSemaphore = new(1, 1);
        private int _reconnectAttempts = 0;
        private const int MaxReconnectDelaySeconds = 300; // 最大重连延迟5分钟
        private bool _isReconnecting = false;
        
        // 实现接口属性
        public bool IsConnected => _mqttClient?.IsConnected ?? false;
        public int ReconnectAttempts => _reconnectAttempts;

        // 定义消息处理委托
        public event Func<string, string, Task>? MessageReceived;

        public MqttService(
            ILogger<MqttService> logger, 
            MqttConfig mqttConfig)
        {
            _logger = logger;
            _mqttConfig = mqttConfig;

            var factory = new MqttFactory();
            _mqttClient = factory.CreateMqttClient();

            var optionsBuilder = new MqttClientOptionsBuilder()
                .WithTcpServer(_mqttConfig.BrokerAddress, _mqttConfig.BrokerPort)
                .WithClientId(_mqttConfig.ClientId)
                .WithCleanSession(false) // 保持会话状态，更可靠
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(60)); // 合理的心跳间隔

            // 如果配置了用户名和密码，则添加认证
            if (!string.IsNullOrEmpty(_mqttConfig.Username))
            {
                optionsBuilder.WithCredentials(_mqttConfig.Username, _mqttConfig.Password);
            }

            _options = optionsBuilder.Build();

            _mqttClient.ApplicationMessageReceivedAsync += HandleReceivedMessageAsync;
            _mqttClient.DisconnectedAsync += HandleDisconnectedAsync;
            
            _logger.LogInformation("MQTT服务已初始化");
        }

        public async Task StartAsync()
        {
            try
            {
                _logger.LogInformation("开始启动MQTT服务...");
                await ConnectWithRetryAsync();
                _logger.LogInformation("MQTT服务启动完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MQTT服务启动失败");
                throw;
            }
        }
        
        private async Task HandleDisconnectedAsync(MqttClientDisconnectedEventArgs e)
        {
            _logger.LogWarning("MQTT客户端断开连接: {Reason}", e.Reason);
            
            // 如果已经在重连过程中，不重复触发
            if (_isReconnecting) return;
            
            await ConnectWithRetryAsync();
        }
        
        private async Task ConnectWithRetryAsync()
        {
            // 防止多线程同时尝试重连
            if (!await _reconnectSemaphore.WaitAsync(0))
            {
                _logger.LogDebug("已有重连过程在进行中");
                return;
            }
            
            try
            {
                _isReconnecting = true;
                
                while (!_mqttClient.IsConnected)
                {
                    try
                    {
                        _reconnectAttempts++;
                        
                        // 指数退避算法计算延迟时间（秒）
                        int delaySeconds = Math.Min(
                            (int)Math.Pow(2, Math.Min(_reconnectAttempts, 8)) * _mqttConfig.ReconnectDelay, 
                            MaxReconnectDelaySeconds);
                        
                        _logger.LogInformation("尝试连接MQTT服务器 (第{Attempt}次尝试), 延迟{DelaySeconds}秒", 
                            _reconnectAttempts, delaySeconds);
                        
                        // 等待指定的延迟时间
                        if (_reconnectAttempts > 1) 
                        {
                            await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
                        }
                        
                        // 尝试连接
                        await _mqttClient.ConnectAsync(_options, CancellationToken.None);
                        
                        if (_mqttClient.IsConnected)
                        {
                            _logger.LogInformation("MQTT客户端已成功连接到 {BrokerAddress}:{BrokerPort}", 
                                _mqttConfig.BrokerAddress, _mqttConfig.BrokerPort);
                            
                            // 重置重连计数
                            _reconnectAttempts = 0;
                            
                            // 连接成功后不再自动订阅主题，由外部服务处理
                            _logger.LogInformation("MQTT客户端已连接，等待外部服务订阅主题");
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "MQTT连接失败 (第{Attempt}次尝试)", _reconnectAttempts);
                    }
                }
            }
            finally
            {
                _isReconnecting = false;
                _reconnectSemaphore.Release();
            }
        }

        public async Task SubscribeAsync(string topic, int qos = 0)
        {
            if (string.IsNullOrEmpty(topic))
            {
                _logger.LogError("无法订阅空主题");
                return;
            }
            
            if (!IsConnected)
            {
                _logger.LogWarning("MQTT客户端未连接，尝试重新连接后再订阅: {Topic}", topic);
                await ConnectWithRetryAsync();
                
                if (!IsConnected)
                {
                    _logger.LogError("无法订阅主题，MQTT客户端连接失败: {Topic}", topic);
                    return;
                }
            }
            
            try
            {
                MqttQualityOfServiceLevel qosLevel = qos switch
                {
                    0 => MqttQualityOfServiceLevel.AtMostOnce,
                    1 => MqttQualityOfServiceLevel.AtLeastOnce,
                    2 => MqttQualityOfServiceLevel.ExactlyOnce,
                    _ => MqttQualityOfServiceLevel.AtMostOnce
                };
                
                await _mqttClient.SubscribeAsync(new MqttClientSubscribeOptionsBuilder()
                    .WithTopicFilter(builder => 
                        builder.WithTopic(topic)
                            .WithQualityOfServiceLevel(qosLevel))
                    .Build());
                
                _logger.LogInformation("已订阅主题: {Topic}, QoS: {QoS}", topic, qos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅主题失败: {Topic}", topic);
            }
        }

        private async Task HandleReceivedMessageAsync(MqttApplicationMessageReceivedEventArgs e)
        {
            try
            {
                string topic = e.ApplicationMessage.Topic;
                var payloadSegment = e.ApplicationMessage.PayloadSegment;
                byte[] payloadBytes = payloadSegment.Array != null 
                    ? payloadSegment.ToArray() 
                    : Array.Empty<byte>();
                string payload = Encoding.UTF8.GetString(payloadBytes);
                string clientId = e.ClientId ?? string.Empty;

                _logger.LogInformation("收到消息: Topic={Topic}, Payload={Payload}", topic, payload);

                // 确保事件处理器安全调用
                var handler = MessageReceived;
                if (handler != null)
                {
                    try
                    {
                        await handler(topic, payload);
                    }
                    catch (Exception handlerEx)
                    {
                        _logger.LogError(handlerEx, "事件处理器执行失败: Topic={Topic}", topic);
                    }
                }
                else
                {
                    _logger.LogWarning("没有消息处理器订阅MessageReceived事件: Topic={Topic}", topic);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理MQTT消息时发生错误");
            }
        }
        
        public async Task PublishMessageAsync(string topic, string payload)
        {
            await PublishMessageAsync(topic, payload, 0, false);
        }
        
        public async Task PublishMessageAsync(string topic, string payload, int qos, bool retain = false)
        {
            if (string.IsNullOrEmpty(topic))
            {
                _logger.LogError("发布消息失败：主题为空");
                return;
            }
            
            // 确保连接可用
            if (!_mqttClient.IsConnected)
            {
                _logger.LogWarning("MQTT客户端未连接，尝试重新连接");
                await ConnectWithRetryAsync();
                
                // 如果重连失败，记录错误并退出
                if (!_mqttClient.IsConnected)
                {
                    _logger.LogError("无法发布消息，MQTT客户端连接失败: Topic={Topic}", topic);
                    return;
                }
            }

            try
            {
                MqttQualityOfServiceLevel qosLevel = qos switch
                {
                    0 => MqttQualityOfServiceLevel.AtMostOnce,
                    1 => MqttQualityOfServiceLevel.AtLeastOnce,
                    2 => MqttQualityOfServiceLevel.ExactlyOnce,
                    _ => MqttQualityOfServiceLevel.AtMostOnce
                };
                
                var message = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(payload)
                    .WithQualityOfServiceLevel(qosLevel)
                    .WithRetainFlag(retain)
                    .Build();

                await _mqttClient.PublishAsync(message, CancellationToken.None);
                
                _logger.LogInformation("消息已发布: Topic={Topic}, QoS={QoS}, Retain={Retain}, Payload={Payload}", topic, qos, retain, payload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布消息时发生错误: Topic={Topic}", topic);
            }
        }

        public void Dispose()
        {
            try
            {
                if (_mqttClient?.IsConnected == true)
                {
                    _mqttClient.DisconnectAsync().GetAwaiter().GetResult();
                }
                _mqttClient?.Dispose();
                _reconnectSemaphore?.Dispose();
                
                _logger.LogInformation("MQTT服务已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭MQTT客户端时发生错误");
            }
        }
    }
} 