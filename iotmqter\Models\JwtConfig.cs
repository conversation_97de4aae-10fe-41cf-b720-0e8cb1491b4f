namespace iotmqter.Models
{
    /// <summary>
    /// JWT配置类
    /// </summary>
    public class JwtConfig
    {
        /// <summary>
        /// JWT密钥
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// JWT颁发者
        /// </summary>
        public string Issuer { get; set; } = string.Empty;

        /// <summary>
        /// JWT接收者
        /// </summary>
        public string Audience { get; set; } = string.Empty;

        /// <summary>
        /// JWT有效期(小时)
        /// </summary>
        public int ExpiryInHours { get; set; } = 24;
    }
} 