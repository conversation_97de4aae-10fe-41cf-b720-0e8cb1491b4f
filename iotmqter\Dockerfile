# 仅使用运行时镜像
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app

# 健康检查工具已禁用以提升构建速度
# RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 复制发布文件 - 这些文件需要在构建镜像前准备好
COPY ./publish/ .

# 设置环境变量
ENV ASPNETCORE_URLS=http://+:80
ENV DOTNET_EnableDiagnostics=0
ENV DOTNET_gcServer=1

# 暴露端口
EXPOSE 80

# 健康检查已禁用以提升Docker构建速度
# HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD curl -f http://localhost:80/health || exit 1

# 运行应用
ENTRYPOINT ["dotnet", "iotmqter.dll"] 