using System.Text.Json.Serialization;
using iotmqter.Models;
using iotmqter.Services;
using System.Text.Json;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using SqlSugar;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using StackExchange.Redis;
using System.Net.Http;
using System.Net;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace iotmqter
{
    public class Program
    {
        // 用于 oauth/bdyb 接口的简单锁对象
        private static readonly object _bdybLock = new object();

        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateSlimBuilder(args);

            // 配置使用环境变量
            builder.Configuration.AddEnvironmentVariables();

            // JWT密钥配置，从环境变量获取
            var jwtKey = builder.Configuration["JWT_KEY"];
            if (string.IsNullOrEmpty(jwtKey) || jwtKey.Length < 16)
            {
                throw new InvalidOperationException("JWT_KEY环境变量未设置或密钥长度不足16字符，请配置有效的JWT密钥");
            }

            builder.Services.AddSingleton(new JwtConfig 
            {
                Key = jwtKey,
                Issuer = builder.Configuration["JWT_ISSUER"] ?? "iotmqter",
                Audience = builder.Configuration["JWT_AUDIENCE"] ?? "",
                ExpiryInHours = 24
            });
            
            // 配置日志，确保包含时间信息
            builder.Logging.ClearProviders();
            builder.Logging.AddSimpleConsole(options => 
            {
                options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss.fff] ";
                options.IncludeScopes = true;
                options.SingleLine = false;
                options.UseUtcTimestamp = false; 
            });
            
            // 配置JSON序列化选项
            builder.Services.ConfigureHttpJsonOptions(options =>
            {
                options.SerializerOptions.WriteIndented = false;
                options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                options.SerializerOptions.PropertyNameCaseInsensitive = true;
            });

            // 从环境变量直接配置MQTT选项
            var mqttConfig = new MqttConfig
            {
                BrokerAddress = builder.Configuration["MQTT_BROKER_ADDR"] ?? "127.0.0.1",
                BrokerPort = int.TryParse(builder.Configuration["MQTT_BROKER_PORT"], out int port) ? port : 1883,
                Username = builder.Configuration["MQTT_USERNAME"] ?? "",
                Password = builder.Configuration["MQTT_PASSWORD"] ?? "",
                ClientId = "iotmqter",
                ReconnectDelay = int.TryParse(builder.Configuration["MQTT_RECONNECT_DELAY"], out int delay) ? delay : 5
            };
            builder.Services.AddSingleton(mqttConfig);
            
            // 添加数据库配置
            var dbConfig = new DbConfig
            {
                ConnectionString = builder.Configuration["DB_CONNECTION_STRING"] ?? "server=************;port=3306;database=tkapi;user=root;password=*"tkd,123*";SslMode=None;pooling=true"
            };
            builder.Services.AddSingleton(dbConfig);
            
            // 添加Redis配置
            var redisConfig = new RedisConfig
            {
                ConnectionString = builder.Configuration["REDIS_CONNECTION_STRING"] ?? "127.0.0.1:6379,defaultDatabase=0"
            };
            builder.Services.AddSingleton(redisConfig);

            // 注册SqlSugar服务 - 使用SqlSugarScope单例模式，内置线程安全机制
            builder.Services.AddSingleton<ISqlSugarClient>(sp => DbService.CreateSqlSugarScope(sp.GetRequiredService<DbConfig>()));
            
            // 注册Redis服务
            builder.Services.AddSingleton<IRedisService, RedisService>();
            
            // 注册ConnectionMultiplexer
            builder.Services.AddSingleton<IConnectionMultiplexer>(sp => 
                ConnectionMultiplexer.Connect(sp.GetRequiredService<RedisConfig>().ConnectionString));

            // 注册服务 - 确保所有服务都使用相同的生命周期(Singleton)
            builder.Services.AddSingleton<GrpIdMapper>();
            builder.Services.AddSingleton<IMqttService, MqttService>();
            builder.Services.AddSingleton<ITransformService, TransformService>();
            
            // 注册JWT服务
            builder.Services.AddSingleton<JwtService>();
            
            // 注册HttpClient工厂，配置高性能SocketsHttpHandler
            builder.Services.AddHttpClient("default")
                .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(15),
                    MaxConnectionsPerServer = 1024,
                    ConnectTimeout = TimeSpan.FromSeconds(15),
                    KeepAlivePingTimeout = TimeSpan.FromSeconds(30),
                    KeepAlivePingDelay = TimeSpan.FromSeconds(60),
                    EnableMultipleHttp2Connections = true
                });
            
            // 添加EMQX API配置
            var emqxConfig = new EmqxConfig
            {
                ApiUrl = builder.Configuration["EMQX_API_URL"] ?? "http://127.0.0.1:18083",
                Username = builder.Configuration["EMQX_API_USERNAME"] ?? "admin",
                Password = builder.Configuration["EMQX_API_PASSWORD"] ?? "public"
            };
            builder.Services.AddSingleton(emqxConfig);
            
            // 注册IServiceProvider到自身，确保可在服务中访问容器
            builder.Services.AddSingleton<IServiceProvider>(sp => sp);
            
            // 使用启动任务确保服务初始化顺序
            builder.Services.AddHostedService<StartupService>();
                           
            // 添加健康检查
            builder.Services.AddHealthChecks()
                           .AddCheck<MqttHealthCheck>("mqtt_health");

            var app = builder.Build();

            // 状态API端点
            app.MapGet("/status", () => new StatusResponse { Status = "Running", Timestamp = DateTime.UtcNow, Version = typeof(Program).Assembly.GetName().Version?.ToString() ?? "1.0.0" });
            
            // 健康检查端点
            app.MapHealthChecks("/health");
            
            // MQTT状态API
            app.MapGet("/mqtt/status", (IMqttService mqttService) => new MqttStatusResponse 
            { 
                IsConnected = mqttService.IsConnected,
                ReconnectAttempts = mqttService.ReconnectAttempts,
                Timestamp = DateTime.UtcNow 
            });
            
            // OAuth登录接口 - 返回JWT令牌
            app.MapPost("/oauth/login", async (LoginRequestModel request, ISqlSugarClient db, JwtService jwtService, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到登录请求：Username={Username}", request.Username);
                
                try
                {
                    // 查询用户信息
                    var user = await db.Queryable<UserEntity>()
                        .Where(u => u.AppId == request.Username && 
                                u.SecretKey == request.Password && 
                                u.IsEnable == 1 && 
                                u.SecretKey != "")
                        .FirstAsync();
                    
                    if (user == null)
                    {
                        logger.LogWarning("登录失败：无效的用户名或密码, Username={Username}", request.Username);
                        return Results.Unauthorized();
                    }
                    
                    // 生成JWT令牌
                    var (token, expireTime) = jwtService.GenerateToken(user.AppId, user.SysId, user.Allow ?? string.Empty);
                    var expireTimestamp = new DateTimeOffset(expireTime).ToUnixTimeSeconds();
                    
                    logger.LogInformation("登录成功：Username={Username}, TokenExpiry={Expiry}", 
                        request.Username, expireTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return Results.Ok(new
                    {
                        success = true,
                        message = "登录成功",
                        token = token,
                        expire = expireTimestamp
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "处理登录请求时发生异常: Username={Username}", request.Username);
                    return Results.BadRequest(new
                    {
                        success = false,
                        message = "登录失败：发生内部错误"
                    });
                }
            });
            
            // 组ID映射状态API
            app.MapGet("/mapper/status", (GrpIdMapper mapper) => new
            {
                MappingCount = mapper.MappingCount,
                Timestamp = DateTime.UtcNow
            });
            
            // MQTT客户端认证API - 验证MQTT客户端连接
            app.MapPost("/mqtt/auth", async (MqttAuthRequestModel request, ISqlSugarClient db, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到MQTT认证请求：ClientId={ClientId}, Username={Username}", request.ClientId, request.Username);
                
                try
                {
                    // 查询MQTT用户信息
                    var mqttUser = await db.Queryable<DeviceEntity>()
                        .Where(u => u.Username == request.Username)
                        .FirstAsync();
                    
                    // 进行认证检查
                    if (mqttUser == null || mqttUser.Password != request.Password || mqttUser.ClientId != request.ClientId)
                    {
                        logger.LogWarning("MQTT认证拒绝：ClientId={ClientId}, Username={Username}, Password={Password}", 
                            request.ClientId, request.Username, request.Password ?? "");
                        
                        return Results.Ok(new { result = "deny" });
                    }
                    
                    logger.LogInformation("MQTT认证通过：ClientId={ClientId}, Username={Username}", request.ClientId, request.Username);
                    return Results.Ok(new { result = "allow" });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "MQTT认证处理异常：ClientId={ClientId}, Username={Username}", request.ClientId, request.Username);
                    return Results.Ok(new { result = "deny" });
                }
            });

            // MQTT设备授权API - 生成设备连接凭证
            app.MapGet("/core/device_license", async (string imei, string imsi, string iccid, string ekey, ISqlSugarClient db, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到设备授权请求：imei={Imei}, imsi={Imsi}, iccid={Iccid}, ekey={Ekey}", imei, imsi, iccid, ekey);
                
                try
                {
                    // 查询应用信息
                    var appInfo = await db.Queryable<UserEntity>()
                        .Where(u => u.AppId == ekey)
                        .FirstAsync();
                    
                    // 验证应用权限
                    if (appInfo == null || appInfo.Allow == null || !appInfo.Allow.Contains("qpy") || appInfo.IsEnable != 1)
                    {
                        logger.LogWarning("设备授权拒绝：应用不存在或无权限, ekey={Ekey}", ekey);
                        return Results.BadRequest(new { success = false, message = "无效的应用密钥或权限不足" });
                    }
                    
                    // 验证IMEI格式
                    if (string.IsNullOrEmpty(imei) || !System.Text.RegularExpressions.Regex.IsMatch(imei, @"^*d+$"))
                    {
                        logger.LogWarning("设备授权拒绝：无效的IMEI格式, imei={Imei}", imei);
                        return Results.BadRequest(new { success = false, message = "无效的IMEI格式" });
                    }
                    
                    // 生成设备凭证
                    string username = "i" + imei;
                    string password = Guid.NewGuid().ToString("N");
                    
                    // 查询设备是否存在
                    var existingDevice = await db.Queryable<DeviceEntity>()
                        .Where(d => d.ClientId == username)
                        .FirstAsync();
                    
                    if (existingDevice != null)
                    {
                        // 更新现有设备信息
                        await db.Updateable<DeviceEntity>()
                            .SetColumns(d => new DeviceEntity
                            {
                                Username = username,
                                Password = password,
                                Imei = imei,
                                Imsi = imsi,
                                Iccid = iccid,
                                UpdateTime = DateTime.Now
                            })
                            .Where(d => d.ClientId == username)
                            .ExecuteCommandAsync();
                            
                        logger.LogInformation("设备授权成功(更新)：clientid={ClientId}", username);
                    }
                    else
                    {
                        // 创建新设备
                        var newDevice = new DeviceEntity
                        {
                            ClientId = username,
                            Username = username,
                            Password = password,
                            Imei = imei,
                            Imsi = imsi,
                            Iccid = iccid,
                            HostId = string.Empty,
                            Unilink = string.Empty,
                            Dispatch = string.Empty,
                            IsOnline = 0,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now
                        };
                        
                        await db.Insertable(newDevice).ExecuteCommandAsync();
                        logger.LogInformation("设备授权成功(新增)：clientid={ClientId}", username);
                    }
                    
                    // 返回设备连接凭证
                    return Results.Ok(new { clientid = username, username = username, password = password });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "设备授权处理异常：imei={Imei}, ekey={Ekey}", imei, ekey);
                    return Results.BadRequest(new { success = false, message = "处理授权请求时发生异常" });
                }
            });
            
            // 网关授权API - 生成网关授权文件
            app.MapGet("/core/gateway_license", async (string cpud, string uuid, string ekey, HttpContext httpContext, ISqlSugarClient db, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到网关授权请求：cpud={Cpud}, uuid={Uuid}, ekey={Ekey}", cpud, uuid, ekey);
                
                try
                {
                    // 查询应用信息
                    var appInfo = await db.Queryable<UserEntity>()
                        .Where(u => u.AppId == ekey)
                        .FirstAsync();
                    
                    // 验证应用权限
                    if (appInfo == null || appInfo.Allow == null || !appInfo.Allow.Contains("gwy") || appInfo.IsEnable != 1)
                    {
                        logger.LogWarning("网关授权拒绝：应用不存在或无权限, ekey={Ekey}", ekey);
                        return Results.BadRequest(new { success = false, message = "无效的应用密钥或权限不足" });
                    }
                    
                    // 创建内存流用于存储生成的授权文件
                    var memoryStream = new MemoryStream();
                    
                    // 检查secret_key是否可用
                    if (!string.IsNullOrEmpty(appInfo.SecretKey))
                    {
                        // 使用RSA加密生成授权信息
                        byte[] licenseData;
                        var rsaProvider = new RSAProviderService();
                        using (var rsa = rsaProvider.CreateFromPublicKey(appInfo.SecretKey))
                        {
                            if (rsa == null)
                            {
                                logger.LogError("创建RSA加密服务失败，可能是公钥格式无效：ekey={Ekey}", ekey);
                                return Results.BadRequest(new { success = false, message = "无效的密钥格式" });
                            }
                            
                            // 加密授权信息
                            string plainText = $"code={cpud}";
                            byte[] encryptedData = rsa.Encrypt(Encoding.UTF8.GetBytes(plainText), RSAEncryptionPadding.Pkcs1);
                            licenseData = Encoding.UTF8.GetBytes(Convert.ToBase64String(encryptedData));
                        }
                        
                        // 查询网关信息是否已存在
                        var existingGateway = await db.Queryable<GatewayEntity>()
                            .Where(g => g.CpuId == cpud)
                            .FirstAsync();
                        
                        if (existingGateway != null)
                        {
                            // 更新现有网关信息
                            await db.Updateable<GatewayEntity>()
                                .SetColumns(g => new GatewayEntity
                                {
                                    Uuid = uuid,
                                    UpdateTime = DateTime.Now
                                })
                                .Where(g => g.CpuId == cpud)
                                .ExecuteCommandAsync();
                                
                            logger.LogInformation("网关信息更新成功：cpud={Cpud}", cpud);
                        }
                        else
                        {
                            // 创建新网关记录
                            var newGateway = new GatewayEntity
                            {
                                CpuId = cpud,
                                Uuid = uuid,
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now
                            };
                            
                            await db.Insertable(newGateway).ExecuteCommandAsync();
                            logger.LogInformation("新网关记录创建成功：cpud={Cpud}", cpud);
                        }
                        
                        // 将授权数据写入内存流
                        memoryStream.Write(licenseData, 0, licenseData.Length);
                        memoryStream.Position = 0;
                        
                        // 记录客户端IP
                        string clientIp = httpContext.Connection.RemoteIpAddress?.ToString() ?? "未知IP";
                        logger.LogInformation("网关授权成功：cpud={Cpud}, 客户端IP={ClientIp}", cpud, clientIp);
                        
                        // 返回授权文件
                        return Results.File(memoryStream, "application/octet-stream", "licx");
                    }
                    else
                    {
                        logger.LogError("网关授权失败：无效的应用密钥, ekey={Ekey}", ekey);
                        return Results.BadRequest(new { success = false, message = "应用密钥无效" });
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "处理网关授权请求时发生异常：cpud={Cpud}, ekey={Ekey}", cpud, ekey);
                    return Results.BadRequest(new { success = false, message = "处理授权请求时发生异常" });
                }
            });
            
            // 北电云表响应API - 处理北电云表响应并写入Redis
            app.MapPost("/bdyb/beidian-response", async (HttpRequest request, IRedisService redisService, IMqttService mqttService, ISqlSugarClient db, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到北电云表响应请求");
                
                try
                {
                    // 读取请求体内容
                    string requestBody;
                    using (StreamReader reader = new StreamReader(request.Body, Encoding.UTF8))
                    {
                        requestBody = await reader.ReadToEndAsync();
                    }
                    
                    // 使用Newtonsoft.Json解析请求体
                    var jsonp = JObject.Parse(requestBody);
                    
                    // 发布Redis消息（兼容方法）
                    if (jsonp["commandId"] != null && jsonp["resultParams"] != null)
                    {
                        string commandId = jsonp["commandId"]?.Value<string>() ?? string.Empty;
                        string resultParams = jsonp["resultParams"]?.ToString(Newtonsoft.Json.Formatting.None) ?? string.Empty;
                        if (!string.IsNullOrEmpty(commandId))
                        {
                            await redisService.PublishAsync("tksrv." + commandId, resultParams);
                            logger.LogInformation("已执行兼容方法，发布Redis消息: Channel={Channel}, Message={MessageLength}字节", commandId, resultParams.Length);
                        }
                    }
                    
                    // 检查是否有必要字段
                    if (jsonp["commandId"] != null && jsonp["resultParams"] != null)
                    {
                        // 获取commandId
                        string commandId = jsonp["commandId"]?.Value<string>() ?? string.Empty;
                        if (string.IsNullOrEmpty(commandId))
                        {
                            logger.LogWarning("请求体中commandId为空或不存在");
                            return Results.BadRequest(new { success = false, message = "请求格式无效" });
                        }
                        
                        // 从Redis获取commandId对应的taskId
                        string redisKey = $"cdata.bdyb.{commandId}";
                        string taskId = await redisService.StringGetAsync(redisKey);

                        // 读取后删除Redis中的键，避免长期占用内存
                        await redisService.KeyDeleteAsync(redisKey);

                        if (string.IsNullOrEmpty(taskId))
                        {
                            logger.LogWarning("未找到commandId对应的taskId: CommandId={CommandId}", commandId);
                        }
                        else
                        {
                            logger.LogInformation("找到commandId对应的taskId: CommandId={CommandId}, TaskId={TaskId}", commandId, taskId);
                        }
                        
                        // 获取设备编号 - 直接从请求体中的devNo字段获取
                        string devNo = jsonp["devNo"]?.Value<string>() ?? string.Empty;
                        if (!string.IsNullOrEmpty(devNo))
                        {
                            logger.LogInformation("从请求体获取设备编号: DevNo={DevNo}", devNo);
                        }
                        else
                        {
                            logger.LogWarning("请求体中无设备编号(devNo)字段");
                        }
                        
                        // 如果找到了taskId，将其添加到响应消息中并发布到Redis通道
                        if (!string.IsNullOrEmpty(taskId))
                        {
                            // 添加taskId到响应消息
                            jsonp["taskid"] = taskId;
                            
                            // 更新requestBody为修改后的JSON
                            requestBody = jsonp.ToString(Newtonsoft.Json.Formatting.None);
                            
                            logger.LogDebug("已将taskId添加到响应消息中: {ModifiedResponse}", requestBody);

                            // 使用PublishAsync发布消息到Redis通道
                            string taskRedisKey = $"guid.{taskId}";
                            await redisService.PublishAsync(taskRedisKey, requestBody);

                            logger.LogInformation("北电云表响应已发布到Redis通道: Channel={Channel}, TaskId={TaskId}", taskRedisKey, taskId);
                        }

                        // 如果获取到了设备编号，则查询设备信息
                        if (!string.IsNullOrEmpty(devNo))
                        {
                            // 查询设备unilink配置
                            var equipBdyb = await db.Queryable<EquipBdybEntity>()
                                .Where(e => e.DevNo == devNo)
                                .FirstAsync();

                            // 转发到MQTT主题，非默认多转发一份
                            string unilink = !string.IsNullOrEmpty(equipBdyb?.Unilink) ? equipBdyb.Unilink : "default";
                            if (unilink != "default")
                            {
                                string t = "v1/vftkt/default/rpc/response";
                                await mqttService.PublishMessageAsync(t, requestBody);
                                logger.LogInformation("北电云表响应已转发到MQTT: Topic={Topic}, DevNo={DevNo}", t, devNo);
                            }
                            string topic = $"v1/vftkt/{unilink}/rpc/response";
                            await mqttService.PublishMessageAsync(topic, requestBody);
                            logger.LogInformation("北电云表响应已转发到MQTT: Topic={Topic}, DevNo={DevNo}", topic, devNo);

                        }

                        return Results.Ok(new { success = true, message = "响应已处理" });
                    }
                    else
                    {
                        logger.LogWarning("北电云表响应格式无效: {RequestBody}", requestBody);
                        return Results.BadRequest(new { success = false, message = "请求格式无效" });
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "处理北电云表响应时发生异常");
                    return Results.BadRequest(new { success = false, message = "处理响应时发生异常" });
                }
            });

            // 添加北电云表令牌接口
            app.MapGet("/oauth/bdyb", async (IRedisService redisService, ILogger<Program> logger) =>
            {
                logger.LogInformation("收到北电云表令牌请求");
                
                try
                {
                    // 尝试从Redis获取令牌（不加锁）
                    string redisKey = "oauth.token.bdyb";
                    string tokenJson = await redisService.StringGetAsync(redisKey);
                    
                    // 如果Redis中已有值，说明令牌还在有效期内，直接返回
                    if (!string.IsNullOrEmpty(tokenJson))
                    {
                        logger.LogInformation("使用缓存的北电云表令牌");
                        var tokenObj = JObject.Parse(tokenJson);
                        return Results.Ok(new 
                        {
                            token = tokenObj["token"]?.Value<string>() ?? string.Empty,
                            expire = tokenObj["expire"]?.Value<long>() ?? 0
                        });
                    }
                    
                    // 只有当Redis中没有令牌时，才加锁获取新令牌
                    lock (_bdybLock)
                    {
                        // 双重检查，避免多线程重复获取
                        tokenJson = redisService.StringGetAsync(redisKey).GetAwaiter().GetResult();
                        if (!string.IsNullOrEmpty(tokenJson))
                        {
                            logger.LogInformation("锁内二次检查发现已有缓存的令牌");
                            var tokenObj = JObject.Parse(tokenJson);
                            return Results.Ok(new 
                            { 
                                token = tokenObj["token"]?.Value<string>() ?? string.Empty,
                                expire = tokenObj["expire"]?.Value<long>() ?? 0
                            });
                        }
                        
                        // 从环境变量获取配置
                        string username = Environment.GetEnvironmentVariable("BDYB_USERNAME") ?? "";
                        string key = Environment.GetEnvironmentVariable("BDYB_KEY") ?? "";
                        string url = Environment.GetEnvironmentVariable("BDYB_URL") ?? "";
                        
                        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(key) || string.IsNullOrEmpty(url))
                        {
                            logger.LogError("北电云表配置缺失，请检查环境变量BDYB_USERNAME、BDYB_KEY和BDYB_URL");
                            return Results.BadRequest(new { success = false, message = "北电云表配置缺失" });
                        }
                        
                        // 构建请求URL
                        long currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        var random = new Random();
                        int randomNum = random.Next(0, int.MaxValue);
                        
                        string urlParams = $"userName={username}&time={currentTimestamp}&num={randomNum}";
                        string signature = SHA256Hash($"{urlParams}&key={key}").ToLowerInvariant();
                        string requestUrl = $"{url}/api/sys/login?{urlParams}&sign={signature}";
                        
                        logger.LogInformation("开始请求新的北电云表令牌");
                        
                        // 使用IHttpClientFactory获取HttpClient实例，而不是直接创建
                        using var httpClient = app.Services.GetRequiredService<IHttpClientFactory>().CreateClient("default");
                        using var response = httpClient.PostAsync(
                            requestUrl, 
                            new FormUrlEncodedContent(new Dictionary<string, string>())
                        ).GetAwaiter().GetResult();
                        
                        if (!response.IsSuccessStatusCode)
                        {
                            logger.LogError("请求北电云表令牌失败，HTTP状态码: {StatusCode}", (int)response.StatusCode);
                            return Results.StatusCode((int)response.StatusCode);
                        }
                        
                        string responseBody = response.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                        
                        // 使用JObject解析响应
                        var responseObj = JObject.Parse(responseBody);
                        
                        // 检查响应结果
                        if (responseObj["code"]?.Value<int>() == 0 && 
                            responseObj["token"] != null && responseObj["expire"] != null)
                        {
                            string token = responseObj["token"]?.Value<string>() ?? string.Empty;
                            long expiresIn = responseObj["expire"]?.Value<long>() ?? 0;
                            
                            if (string.IsNullOrEmpty(token) || expiresIn <= 0)
                            {
                                logger.LogError("北电云表返回的token或expire无效: Token={Token}, ExpiresIn={ExpiresIn}", token, expiresIn);
                                return Results.BadRequest(new { success = false, message = "获取令牌失败，服务返回无效数据" });
                            }
                            
                            // 计算过期时间（当前时间 + 有效期 - 10秒缓冲）
                            long expireTime = currentTimestamp + expiresIn - 10;
                            
                            // 创建要缓存的数据
                            var tokenData = new 
                            {
                                token = token,
                                expire = expireTime
                            };
                            
                            // 序列化并保存到Redis，设置过期时间
                            string tokenDataJson = JsonConvert.SerializeObject(tokenData);
                            int expireSeconds = (int)expiresIn - 10; // 减去10秒作为缓冲
                            
                            redisService.StringSetAsync(redisKey, tokenDataJson, expireSeconds).GetAwaiter().GetResult();
                            logger.LogInformation("获取并缓存了新的北电云表令牌，过期时间: {ExpireTime}", DateTimeOffset.FromUnixTimeSeconds(expireTime).ToString("yyyy-MM-dd HH:mm:ss"));
                            
                            return Results.Ok(tokenData);
                        }
                        else
                        {
                            logger.LogError("北电云表返回无效响应: {Response}", responseBody);
                            return Results.BadRequest(new { success = false, message = "获取令牌失败，服务返回无效响应" });
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "获取北电云表令牌时发生异常");
                    return Results.StatusCode((int)HttpStatusCode.InternalServerError);
                }
            });


            // 初始化数据库
            try
            {
                var logger = app.Services.GetRequiredService<ILogger<Program>>();
                
                // 直接获取SqlSugarScope单例，线程安全无需作用域
                var db = app.Services.GetRequiredService<ISqlSugarClient>();
                
                logger.LogInformation("正在初始化数据库...");
                
                // 验证SqlSugarScope单例状态
                var (isSingleton, hashCode) = DbService.VerifySingletonStatus(db);
                logger.LogInformation("SqlSugar单例验证: IsSingleton={IsSingleton}, HashCode={HashCode}", 
                    isSingleton, hashCode);
                
                if (!isSingleton)
                {
                    logger.LogWarning("警告：数据库客户端不是SqlSugarScope单例模式");
                }
                
                var result = await DbService.InitDatabaseAsync(db);
                logger.LogInformation("数据库初始化{Result}", result ? "成功" : "失败");
            }
            catch (Exception ex)
            {
                app.Services.GetRequiredService<ILogger<Program>>().LogError(ex, "初始化数据库时发生异常");
            }

            await app.RunAsync();
        }

        // SHA256哈希计算辅助方法
        private static string SHA256Hash(string input)
        {
            using SHA256 sha256 = SHA256.Create();
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = sha256.ComputeHash(inputBytes);
            
            StringBuilder builder = new();
            for (int i = 0; i < hashBytes.Length; i++)
            {
                builder.Append(hashBytes[i].ToString("x2"));
            }
            
            return builder.ToString();
        }
    }

    // 状态响应类，用于序列化
    public class StatusResponse
    {
        public string? Status { get; set; }
        public DateTime Timestamp { get; set; }

        public string? Version { get; set; }
    }
    
    // MQTT状态响应类
    public class MqttStatusResponse
    {
        public bool IsConnected { get; set; }
        public int ReconnectAttempts { get; set; }
        public DateTime Timestamp { get; set; }
    }

    // MQTT认证请求模型
    public class MqttAuthRequestModel
    {
        public string? ClientId { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
    }


    // 启动服务，确保服务初始化和启动的正确顺序
    public class StartupService : IHostedService
    {
        private readonly IMqttService _mqttService;
        private readonly ITransformService _transformService;
        private readonly ILogger<StartupService> _logger;

        public StartupService(
            IMqttService mqttService, 
            ITransformService transformService,
            ILogger<StartupService> logger)
        {
            _mqttService = mqttService;
            _transformService = transformService;
            _logger = logger;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("启动服务...");

                // 确保TransformService已实例化，这样它会首先注册MqttService的事件
                // TransformService在构造函数中已经订阅了MqttService.MessageReceived事件
                _logger.LogInformation("确认TransformService已初始化: {ServiceType}", _transformService.GetType().Name);

                // 然后启动MqttService
                _logger.LogInformation("启动MQTT服务...");
                await _mqttService.StartAsync();
                _logger.LogInformation("MQTT服务已启动，连接状态: {Connected}", _mqttService.IsConnected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "服务启动失败");
                throw;
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("停止服务...");
                
                if (_mqttService is IDisposable disposable)
                {
                    disposable.Dispose();
                }
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止服务时发生错误");
                return Task.CompletedTask;
            }
        }
    }
    
    // MQTT健康检查
    public class MqttHealthCheck : IHealthCheck
    {
        private readonly IMqttService _mqttService;
        
        public MqttHealthCheck(IMqttService mqttService)
        {
            _mqttService = mqttService;
        }
        
        public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            if (_mqttService.IsConnected)
            {
                return Task.FromResult(HealthCheckResult.Healthy("MQTT连接正常"));
            }
            
            return Task.FromResult(HealthCheckResult.Unhealthy($"MQTT连接断开，重连尝试: {_mqttService.ReconnectAttempts}"));
        }
    }
}
