# MQTT Configuration
MQTT_BROKER_ADDR=172.16.47.71
MQTT_BROKER_PORT=1883
MQTT_USERNAME=admin
MQTT_PASSWORD=qtxutwxbfcyfvedkpsqqcgtehsgafeea
MQTT_RECONNECT_DELAY=5

# Database Configuration
DB_CONNECTION_STRING=server=172.16.47.71;port=3306;database=tkapi;user=root;password="tkd,123";SslMode=None;pooling=true

# Redis Configuration
REDIS_CONNECTION_STRING=172.16.47.71:6379

# JWT Authentication (MUST be 16+ characters for production)
JWT_KEY=aB7cD9eF2gH5jK8mN1pQ4rS6tU3vW0xY
JWT_ISSUER=iotmqter
JWT_AUDIENCE=

# BeiDian Cloud Table API
BDYB_USERNAME=tianchuangda
BDYB_KEY=9c3ff985a50d4453b5d19f5197b827
BDYB_URL=https://devapi.beidiancloud.cn/machine-cloud

# EMQX API Integration
EMQX_API_URL=http://172.16.47.71:18083
EMQX_API_USERNAME=3c3ac0dc8e6a2c99
EMQX_API_PASSWORD=9CM89Aiczt7eyGARk8WlXcpnDV6p0uW5vFDXMG4Om9AYnN

# System Configuration
ASPNETCORE_ENVIRONMENT=Production
TZ=Asia/Shanghai
API_URL=http://127.0.0.1:80