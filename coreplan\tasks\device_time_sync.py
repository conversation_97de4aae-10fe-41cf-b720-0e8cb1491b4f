"""
设备时钟同步任务模块 - 重构版本
定期从MySQL数据库查询设备并通过EMQX API发送时间同步指令
"""

import time
import struct
import datetime
import requests
import binascii
import pymysql
import json
from typing import Dict, Any, List

from .base_task import BaseTask


class DeviceTimeSyncTask(BaseTask):
    """设备时钟同步任务"""
    
    def __init__(self):
        super().__init__("设备时钟同步")
        
    def get_required_parameters(self) -> list:
        """获取必需参数"""
        return ['mysql', 'emqx']
        
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """验证参数"""
        mysql_config = parameters.get('mysql', {})
        emqx_config = parameters.get('emqx', {})
        
        # 验证MySQL配置
        required_mysql = ['host', 'user', 'database']
        for field in required_mysql:
            if not mysql_config.get(field):
                self.logger.error(f"MySQL配置缺少必需字段: {field}")
                return False
                
        # 验证EMQX配置  
        required_emqx = ['api_url', 'api_username', 'api_password']
        for field in required_emqx:
            if not emqx_config.get(field):
                self.logger.error(f"EMQX配置缺少必需字段: {field}")
                return False
                
        return True
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """执行时钟同步任务"""
        mysql_config = parameters.get('mysql', {})
        emqx_config = parameters.get('emqx', {})
        
        # 查询设备
        devices = self._query_devices(mysql_config)
        if not devices:
            self.logger.warning("没有查询到需要同步时间的设备")
            return True
            
        # 构建时间同步数据包
        hex_data = self._build_time_packet()
        self.logger.info(f"生成时间同步数据包: {hex_data}")
        
        # 发送同步指令
        success = self._send_sync_commands(devices, hex_data, emqx_config)
        
        if success:
            self.logger.info(f"时钟同步完成，共同步 {len(devices)} 个设备")
        else:
            self.logger.error("时钟同步部分失败")
            
        return success
        
    def _query_devices(self, mysql_config: Dict[str, Any]) -> List[str]:
        """查询需要同步的设备"""
        devices = []
        connection = None
        
        try:
            connection = pymysql.connect(
                host=mysql_config['host'],
                port=mysql_config.get('port', 3306),
                user=mysql_config['user'], 
                password=mysql_config.get('password', ''),
                database=mysql_config['database'],
                charset=mysql_config.get('charset', 'utf8mb4')
            )
            
            self.logger.info(f"连接MySQL: {mysql_config['host']}:{mysql_config.get('port', 3306)}")
            
            with connection.cursor() as cursor:
                sql = """
                    SELECT clientid FROM tb_device 
                    WHERE product = 'tkdtu' 
                    AND (branchv = 'sl' OR branchv = '' OR branchv IS NULL)
                    AND clientid IS NOT NULL AND clientid != ''
                """
                cursor.execute(sql)
                results = cursor.fetchall()
                
                devices = [row[0] for row in results if row[0]]
                    
            self.logger.info(f"查询到 {len(devices)} 个设备")
            
        except Exception as e:
            self.logger.error(f"数据库查询失败: {e}")
        finally:
            if connection:
                connection.close()
                
        return devices
        
    def _build_time_packet(self) -> str:
        """构建Modbus时间同步数据包"""
        now = datetime.datetime.now()
        weekday = now.weekday() + 1
        if weekday == 7:
            weekday = 7
            
        # Modbus RTU写多个寄存器请求
        packet = bytearray([0x01, 0x10, 0x01, 0x2E, 0x00, 0x07, 0x0E])
        
        # 添加时间数据 (年月日周时分秒)
        packet.extend(struct.pack('>H', now.year))
        packet.extend(struct.pack('>H', now.month)) 
        packet.extend(struct.pack('>H', now.day))
        packet.extend(struct.pack('>H', weekday))
        packet.extend(struct.pack('>H', now.hour))
        packet.extend(struct.pack('>H', now.minute))
        packet.extend(struct.pack('>H', now.second))
        
        # 计算CRC校验码
        crc_low, crc_high = self._calculate_crc16(packet)
        packet.append(crc_low)
        packet.append(crc_high)
        
        return binascii.hexlify(packet).decode('utf-8').upper()
        
    def _calculate_crc16(self, data) -> tuple:
        """计算Modbus CRC16校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc = crc >> 1
        return crc & 0xFF, (crc >> 8) & 0xFF
        
    def _send_sync_commands(self, devices: List[str], hex_data: str, emqx_config: Dict[str, Any]) -> bool:
        """发送时间同步指令"""
        try:
            # 构建批量发布请求
            topics_payloads = []
            for clientid in devices:
                topics_payloads.append({
                    "topic": f"device/{clientid}/broad",
                    "payload": json.dumps({"data": hex_data}),
                    "qos": 0,
                    "retain": False
                })
                
            # 分批发送
            batch_size = emqx_config.get('batch_size', 100)
            success_count = 0
            total_batches = (len(topics_payloads) + batch_size - 1) // batch_size
            
            for i in range(0, len(topics_payloads), batch_size):
                batch = topics_payloads[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                self.logger.info(f"发送第 {batch_num}/{total_batches} 批，{len(batch)} 个设备")
                
                if self._send_batch_to_emqx(batch, emqx_config):
                    success_count += len(batch)
                else:
                    self.logger.error(f"第 {batch_num} 批发送失败")
                    
            self.logger.info(f"发送完成: {success_count}/{len(devices)}")
            return success_count == len(devices)
            
        except Exception as e:
            self.logger.error(f"发送同步指令异常: {e}")
            return False
            
    def _send_batch_to_emqx(self, batch: List[Dict], emqx_config: Dict[str, Any]) -> bool:
        """发送单批消息到EMQX"""
        try:
            url = f"{emqx_config['api_url']}/api/v5/publish/bulk"
            auth = (emqx_config['api_username'], emqx_config['api_password'])
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            timeout = emqx_config.get('timeout', 30)
            response = requests.post(url, json=batch, auth=auth, headers=headers, timeout=timeout)
            
            if response.status_code == 200:
                return True
            else:
                self.logger.error(f"EMQX API请求失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送批量消息异常: {e}")
            return False


# 为了向后兼容，保留原有的execute函数接口
def execute(parameters: Dict[str, Any], logger) -> bool:
    """向后兼容的执行函数"""
    task = DeviceTimeSyncTask()
    return task.execute(parameters, logger)