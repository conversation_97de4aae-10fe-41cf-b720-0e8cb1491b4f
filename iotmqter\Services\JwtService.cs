using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using iotmqter.Models;
using Microsoft.IdentityModel.Tokens;

namespace iotmqter.Services
{
    /// <summary>
    /// JWT服务实现
    /// </summary>
    public class JwtService
    {
        private readonly JwtConfig _jwtConfig;

        public JwtService(JwtConfig jwtConfig)
        {
            _jwtConfig = jwtConfig;
        }

        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="userId">用户ID</param>
        /// <param name="allowList">授权列表</param>
        /// <returns>JWT令牌信息</returns>
        public (string token, DateTime expireAt) GenerateToken(string username, int userId, string allowList)
        {
            var symmetricKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtConfig.Key));
            var credentials = new SigningCredentials(symmetricKey, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, username),
                new Claim(ClaimTypes.NameIdentifier, userId.ToString()),
                new Claim("allow", allowList)
            };

            var expireTime = DateTime.Now.AddHours(_jwtConfig.ExpiryInHours);
            var token = new JwtSecurityToken(
                issuer: _jwtConfig.Issuer,
                audience: _jwtConfig.Audience,
                claims: claims,
                expires: expireTime,
                signingCredentials: credentials
            );

            return (new JwtSecurityTokenHandler().WriteToken(token), expireTime);
        }
    }
} 