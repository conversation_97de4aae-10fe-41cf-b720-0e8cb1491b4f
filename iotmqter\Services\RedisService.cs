using iotmqter.Models;
using StackExchange.Redis;
using Microsoft.Extensions.Logging;
using System.Text;

namespace iotmqter.Services
{
    /// <summary>
    /// Redis服务接口
    /// </summary>
    public interface IRedisService
    {
        /// <summary>
        /// 设置字符串值，带有效期
        /// </summary>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        /// <param name="expireSeconds">过期时间（秒）</param>
        /// <returns>是否成功</returns>
        Task<bool> StringSetAsync(string key, string value, int expireSeconds);

        /// <summary>
        /// 获取字符串值
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>值</returns>
        Task<string> StringGetAsync(string key);
        
        /// <summary>
        /// 删除键
        /// </summary>
        /// <param name="key">要删除的键</param>
        /// <returns>是否成功</returns>
        Task<bool> KeyDeleteAsync(string key);
        
        /// <summary>
        /// 在Redis通道上发布消息
        /// </summary>
        /// <param name="channel">通道名称</param>
        /// <param name="message">消息内容</param>
        /// <returns>接收到消息的客户端数量</returns>
        Task<long> PublishAsync(string channel, string message);
    }

    /// <summary>
    /// Redis服务实现
    /// </summary>
    public class RedisService : IRedisService
    {
        private readonly ConnectionMultiplexer _redis;
        private readonly IDatabase _db;
        private readonly ILogger<RedisService> _logger;

        /// <summary>
        /// 初始化Redis服务
        /// </summary>
        public RedisService(ILogger<RedisService> logger, RedisConfig redisConfig)
        {
            _logger = logger;

            try
            {
                _redis = ConnectionMultiplexer.Connect(redisConfig.ConnectionString);
                _db = _redis.GetDatabase();
                _logger.LogInformation("Redis服务已初始化，连接到 {ConnectionString}", redisConfig.ConnectionString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis服务初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 设置字符串值，带有效期
        /// </summary>
        public async Task<bool> StringSetAsync(string key, string value, int expireSeconds)
        {
            try
            {
                var timeSpan = TimeSpan.FromSeconds(expireSeconds);
                var result = await _db.StringSetAsync(key, value, timeSpan);
                _logger.LogDebug("Redis设置值: Key={Key}, Value={Value}, ExpireSeconds={ExpireSeconds}", key, value, expireSeconds);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis设置值失败: Key={Key}, Value={Value}", key, value);
                return false;
            }
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        public async Task<string> StringGetAsync(string key)
        {
            try
            {
                var result = await _db.StringGetAsync(key);
                var stringValue = result.ToString() ?? string.Empty;
                _logger.LogDebug("Redis获取值: Key={Key}, Value={Value}", key, stringValue);
                return stringValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis获取值失败: Key={Key}", key);
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 删除键
        /// </summary>
        public async Task<bool> KeyDeleteAsync(string key)
        {
            try
            {
                var result = await _db.KeyDeleteAsync(key);
                _logger.LogDebug("Redis删除键: Key={Key}, Result={Result}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis删除键失败: Key={Key}", key);
                return false;
            }
        }
        
        /// <summary>
        /// 在Redis通道上发布消息
        /// </summary>
        public async Task<long> PublishAsync(string channel, string message)
        {
            try
            {
                var subscriber = _redis.GetSubscriber();
                var result = await subscriber.PublishAsync(RedisChannel.Literal(channel), message);
                _logger.LogDebug("Redis发布消息: Channel={Channel}, Message={MessageLength}字节, Result={Result}", channel, message.Length, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis发布消息失败: Channel={Channel}", channel);
                return 0;
            }
        }
    }
} 