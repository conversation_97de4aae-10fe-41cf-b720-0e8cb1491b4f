using SqlSugar;
using System;

namespace iotmqter.Models
{
    /// <summary>
    /// Dwin设备实体类
    /// </summary>
    [SugarTable("tb_equip_dwin")]
    [SugarIndex("idx_equip_dwin_clientid", nameof(ClientId), OrderByType.Asc, true)]
    [SugarIndex("idx_equip_dwin_bound_code", nameof(BoundCode), OrderByType.Asc)]
    public class EquipDwinEntity
    {
        /// <summary>
        /// 系统ID（主键）
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnName = "sysid")]
        public int SysId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [SugarColumn(ColumnName = "clientid")]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 绑定编码
        /// </summary>
        [SugarColumn(ColumnName = "bound_code")]
        public string BoundCode { get; set; } = string.Empty;

        /// <summary>
        /// IMEI号
        /// </summary>
        [SugarColumn(ColumnName = "imei")]
        public string? Imei { get; set; }

        /// <summary>
        /// ICCID号
        /// </summary>
        [SugarColumn(ColumnName = "iccid")]
        public string? Iccid { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [SugarColumn(ColumnName = "version")]
        public string? Version { get; set; }

        /// <summary>
        /// 上报数据
        /// </summary>
        [SugarColumn(ColumnName = "report_data")]
        public string? ReportData { get; set; }

        /// <summary>
        /// 是否锁定
        /// </summary>
        [SugarColumn(ColumnName = "is_locked")]
        public int? IsLocked { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "create_time")]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnName = "update_time")]
        public DateTime? UpdateTime { get; set; }
    }
} 