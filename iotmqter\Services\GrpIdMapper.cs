using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;

namespace iotmqter.Services
{
    /// <summary>
    /// GrpIdMapper 负责管理 grpid 和 grpid_alias 之间的映射
    /// 实现了线程安全的映射处理，使用自增ID作为短别名
    /// </summary>
    public class GrpIdMapper
    {
        private readonly ConcurrentDictionary<string, int> _grpIdToAlias = new();
        private readonly ConcurrentDictionary<int, string> _aliasToGrpId = new();
        private long _lastId = 0;
        private const int MaxId = int.MaxValue - 1000; // 预留安全边界
        private readonly ILogger<GrpIdMapper> _logger;
        
        public GrpIdMapper(ILogger<GrpIdMapper> logger)
        {
            _logger = logger;
            _logger.LogInformation("GrpIdMapper已初始化");
        }

        /// <summary>
        /// 获取 grpid 对应的 alias，如果不存在则创建
        /// </summary>
        /// <param name="grpId">原始的组ID</param>
        /// <returns>短的数字别名</returns>
        public int GetOrCreateAlias(string grpId)
        {
            if (string.IsNullOrEmpty(grpId))
            {
                _logger.LogWarning("尝试为空的grpId创建别名");
                return 0; // 对于空值返回默认值
            }

            // 如果已经存在映射，直接返回
            if (_grpIdToAlias.TryGetValue(grpId, out int existingAlias))
            {
                _logger.LogDebug("返回现有的grpId别名映射: {GrpId} -> {Alias}", grpId, existingAlias);
                return existingAlias;
            }

            try
            {
                // 原子操作创建新的别名，并处理溢出情况
                long nextId = Interlocked.Increment(ref _lastId);
                
                // 处理溢出，从1重新开始（因为0可能有特殊含义）
                if (nextId >= MaxId)
                {
                    lock (this)
                    {
                        // 如果已经达到最大ID，并且我们有参与重置的权限，则重置为1
                        if (_lastId >= MaxId)
                        {
                            _lastId = 1;
                            nextId = 1;
                            _logger.LogWarning("ID序列已重置为: {NextId}", nextId);
                        }
                        else
                        {
                            // 如果在锁内发现已经被其他线程重置了，则读取当前值
                            nextId = _lastId;
                        }
                    }
                }
                
                int newAlias = (int)nextId;
                
                // 先检查这个新生成的别名是否已被占用
                if (_aliasToGrpId.TryGetValue(newAlias, out string? existingGrpId))
                {
                    _logger.LogWarning("ID冲突，别名 {Alias} 已被分配给 {ExistingGrpId}", newAlias, existingGrpId);
                    
                    // 如果别名已经分配给了其他grpId，则返回那个grpId对应的别名
                    if (_grpIdToAlias.TryGetValue(grpId, out existingAlias))
                    {
                        return existingAlias;
                    }
                    
                    // 如果由于某种原因我们遇到冲突但没有为当前grpId分配别名，则递归重试
                    _logger.LogInformation("递归重试为 {GrpId} 创建别名", grpId);
                    return GetOrCreateAlias(grpId);
                }
                
                // 尝试添加映射，如果在此期间已被其他线程添加，则使用已存在的值
                if (_grpIdToAlias.TryAdd(grpId, newAlias) && _aliasToGrpId.TryAdd(newAlias, grpId))
                {
                    _logger.LogInformation("创建新的grpId别名映射: {GrpId} -> {Alias}", grpId, newAlias);
                    return newAlias;
                }
                
                // 如果添加失败，说明在此期间有其他线程已经添加了映射，重新获取
                if (_grpIdToAlias.TryGetValue(grpId, out existingAlias))
                {
                    _logger.LogDebug("竞争条件下使用现有的grpId别名映射: {GrpId} -> {Alias}", grpId, existingAlias);
                    return existingAlias;
                }
                
                // 如果仍然获取失败，递归重试（这种情况应该极少发生）
                _logger.LogWarning("无法获取现有映射，递归重试: {GrpId}", grpId);
                return GetOrCreateAlias(grpId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建grpId别名时发生错误: {GrpId}", grpId);
                return 0; // 发生异常时返回默认值
            }
        }

        /// <summary>
        /// 根据alias获取原始的grpId
        /// </summary>
        /// <param name="alias">数字别名</param>
        /// <returns>原始的组ID，如果不存在则返回null</returns>
        public string? GetGrpId(int alias)
        {
            if (_aliasToGrpId.TryGetValue(alias, out string? grpId))
            {
                _logger.LogDebug("获取别名对应的grpId: {Alias} -> {GrpId}", alias, grpId);
                return grpId;
            }
            
            _logger.LogWarning("找不到别名对应的grpId: {Alias}", alias);
            return null;
        }
        
        /// <summary>
        /// 获取当前映射数量
        /// </summary>
        public int MappingCount => _grpIdToAlias.Count;
    }
} 