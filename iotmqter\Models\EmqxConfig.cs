namespace iotmqter.Models
{
    /// <summary>
    /// EMQX API配置
    /// </summary>
    public class EmqxConfig
    {
        /// <summary>
        /// EMQX API路径
        /// </summary>
        public string ApiUrl { get; set; } = string.Empty;

        /// <summary>
        /// EMQX API用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// EMQX API密码
        /// </summary>
        public string Password { get; set; } = string.Empty;
    }
} 