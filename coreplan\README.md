# CorePlan - Python定时任务系统

Python定时执行集合，使用YAML配置文件统一管理所有任务。

## 快速开始

```bash
# 安装依赖
pip install -r requirements.txt

# 启动调度器
python scheduler.py
```

## 项目结构

```
coreplan/
├── config.yaml         # 主配置文件
├── scheduler.py         # 核心调度器
├── tasks/              # 任务模块目录
│   ├── data_backup.py          # 数据备份
│   ├── system_monitor.py       # 系统监控
│   ├── api_health_check.py     # API健康检查
│   ├── cleanup_temp.py         # 临时文件清理
│   └── device_time_sync.py     # 设备时钟同步
├── utils/              # 工具模块
└── logs/               # 日志目录
```

## 核心功能

### 设备时钟同步
- 每天06:00自动执行
- 从MySQL查询设备列表
- 通过EMQX API批量发送时间同步指令

### 系统监控
- 监控CPU、内存、磁盘使用率
- 超过阈值自动告警

### 其他任务
- 数据备份和过期清理
- API状态检查
- 临时文件清理

## 配置说明

主要配置在`config.yaml`中：

```yaml
# MySQL数据库配置
mysql:
  host: "localhost"
  user: "root"
  password: "your_password"
  database: "your_database"

# EMQX API配置  
emqx:
  api_url: "http://************:18083"
  api_username: "your_username"
  api_password: "your_password"

# 任务配置
tasks:
  device_time_sync:
    enabled: true
    schedule:
      type: "cron"
      expression: "0 0 6 * * *"  # 每天06:00
```

## 添加新任务

1. 在`tasks/`目录创建新模块
2. 实现`execute(parameters, logger)`方法
3. 在`config.yaml`中添加任务配置

## 日志

- 调度器日志：`logs/scheduler.log`
- 任务日志：`logs/task_name.log`
- 监控数据：`logs/system_metrics.json`