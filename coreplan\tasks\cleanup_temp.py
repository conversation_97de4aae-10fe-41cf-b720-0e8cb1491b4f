"""
临时文件清理任务模块
定期清理指定目录中的临时文件和过期文件
"""

import os
import time
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List

from .base_task import BaseTask


class CleanupTempTask(BaseTask):
    """临时文件清理任务"""
    
    def __init__(self):
        super().__init__("临时文件清理")
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """执行临时文件清理任务"""
        temp_dirs = parameters.get('temp_dirs', ['/tmp'])
        max_age_days = parameters.get('max_age_days', 3)
        file_patterns = parameters.get('file_patterns', ['*'])
        min_size_mb = parameters.get('min_size_mb', 0)
        max_size_mb = parameters.get('max_size_mb', None)
        dry_run = parameters.get('dry_run', False)
        
        cutoff_time = time.time() - (max_age_days * 24 * 3600)
        total_deleted = 0
        total_size_freed = 0
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                self.logger.warning(f"目录不存在: {temp_dir}")
                continue
                
            if not os.path.isdir(temp_dir):
                self.logger.warning(f"路径不是目录: {temp_dir}")
                continue
                
            self.logger.info(f"清理目录: {temp_dir}")
            
            deleted_count, size_freed = self._cleanup_directory(
                temp_dir, cutoff_time, file_patterns, 
                min_size_mb, max_size_mb, dry_run
            )
            
            total_deleted += deleted_count
            total_size_freed += size_freed
            
        size_freed_mb = total_size_freed / (1024 * 1024)
        
        if dry_run:
            self.logger.info(f"模拟清理完成: 将删除 {total_deleted} 个文件，释放 {size_freed_mb:.2f}MB 空间")
        else:
            self.logger.info(f"清理完成: 删除了 {total_deleted} 个文件，释放了 {size_freed_mb:.2f}MB 空间")
            
        return True
    def _cleanup_directory(
        self,
        directory: str, 
        cutoff_time: float, 
        file_patterns: List[str],
        min_size_mb: float,
        max_size_mb: float,
        dry_run: bool
    ) -> tuple:
        """清理单个目录"""
    deleted_count = 0
    size_freed = 0
    
    try:
        for root, dirs, files in os.walk(directory):
            # 清理文件
            for filename in files:
                filepath = os.path.join(root, filename)
                
                try:
                    # 检查文件是否符合删除条件
                    if self._should_delete_file(filepath, cutoff_time, file_patterns, min_size_mb, max_size_mb):
                        file_size = os.path.getsize(filepath)
                        
                        if dry_run:
                            self.logger.debug(f"[模拟] 将删除文件: {filepath} ({file_size} bytes)")
                        else:
                            os.remove(filepath)
                            self.logger.debug(f"删除文件: {filepath} ({file_size} bytes)")
                            
                        deleted_count += 1
                        size_freed += file_size
                        
                except (OSError, PermissionError) as e:
                    self.logger.warning(f"无法删除文件 {filepath}: {e}")
                except Exception as e:
                    self.logger.error(f"处理文件 {filepath} 时出错: {e}")
                    
            # 清理空目录
            for dirname in dirs:
                dirpath = os.path.join(root, dirname)
                
                try:
                    if self._should_delete_directory(dirpath, cutoff_time):
                        if dry_run:
                            self.logger.debug(f"[模拟] 将删除空目录: {dirpath}")
                        else:
                            try:
                                os.rmdir(dirpath)
                                self.logger.debug(f"删除空目录: {dirpath}")
                            except OSError:
                                # 目录不为空，跳过
                                pass
                                
                except Exception as e:
                    self.logger.error(f"处理目录 {dirpath} 时出错: {e}")
                    
    except Exception as e:
        self.logger.error(f"遍历目录 {directory} 时出错: {e}")
        
    return deleted_count, size_freed
    def _should_delete_file(
        self,
        filepath: str, 
        cutoff_time: float, 
        file_patterns: List[str],
        min_size_mb: float,
        max_size_mb: float
    ) -> bool:
        """判断文件是否应该被删除"""
    try:
        # 检查文件修改时间
        mtime = os.path.getmtime(filepath)
        if mtime > cutoff_time:
            return False
            
        # 检查文件大小
        file_size = os.path.getsize(filepath)
        file_size_mb = file_size / (1024 * 1024)
        
        if file_size_mb < min_size_mb:
            return False
            
        if max_size_mb is not None and file_size_mb > max_size_mb:
            return False
            
        # 检查文件名模式
        if file_patterns and file_patterns != ['*']:
            import fnmatch
            filename = os.path.basename(filepath)
            
            match_found = False
            for pattern in file_patterns:
                if fnmatch.fnmatch(filename, pattern):
                    match_found = True
                    break
                    
            if not match_found:
                return False
                
        return True
        
    except Exception:
        return False
    def _should_delete_directory(self, dirpath: str, cutoff_time: float) -> bool:
        """判断目录是否应该被删除（仅删除空目录）"""
    try:
        # 检查目录是否为空
        if os.listdir(dirpath):
            return False
            
        # 检查目录修改时间
        mtime = os.path.getmtime(dirpath)
        return mtime < cutoff_time
        
    except Exception:
        return False


# 为了向后兼容，保留原有的execute函数接口
def execute(parameters: Dict[str, Any], logger) -> bool:
    """向后兼容的执行函数"""
    task = CleanupTempTask()
    return task.execute(parameters, logger)