"""
系统监控任务模块
监控系统资源使用情况（CPU、内存、磁盘等）
"""

import psutil
import json
from datetime import datetime
from typing import Dict, Any

from .base_task import BaseTask


class SystemMonitorTask(BaseTask):
    """系统监控任务"""
    
    def __init__(self):
        super().__init__("系统监控")
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """执行系统监控任务"""
        check_disk = parameters.get('check_disk', True)
        check_memory = parameters.get('check_memory', True) 
        check_cpu = parameters.get('check_cpu', True)
        alert_threshold = parameters.get('alert_threshold', 80)
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'alerts': []
        }
        
        # CPU监控
        if check_cpu:
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics['cpu_percent'] = cpu_percent
            logger.info(f"CPU使用率: {cpu_percent}%")
            
            if cpu_percent > alert_threshold:
                alert = f"CPU使用率过高: {cpu_percent}%"
                metrics['alerts'].append(alert)
                logger.warning(alert)
                
        # 内存监控
        if check_memory:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            metrics['memory_percent'] = memory_percent
            metrics['memory_available_gb'] = round(memory_available_gb, 2)
            
            logger.info(f"内存使用率: {memory_percent}%, 可用内存: {memory_available_gb:.2f}GB")
            
            if memory_percent > alert_threshold:
                alert = f"内存使用率过高: {memory_percent}%"
                metrics['alerts'].append(alert)
                logger.warning(alert)
                
        # 磁盘监控
        if check_disk:
            disk_metrics = []
            
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    used_percent = (usage.used / usage.total) * 100
                    free_gb = usage.free / (1024**3)
                    
                    disk_info = {
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'used_percent': round(used_percent, 2),
                        'free_gb': round(free_gb, 2)
                    }
                    
                    disk_metrics.append(disk_info)
                    
                    logger.info(f"磁盘 {partition.device}: 使用率 {used_percent:.2f}%, "
                              f"剩余空间 {free_gb:.2f}GB")
                    
                    if used_percent > alert_threshold:
                        alert = f"磁盘 {partition.device} 使用率过高: {used_percent:.2f}%"
                        metrics['alerts'].append(alert)
                        logger.warning(alert)
                        
                except PermissionError:
                    # 跳过无权限访问的分区
                    continue
                    
            metrics['disks'] = disk_metrics
            
        # 网络监控（可选）
        network_io = psutil.net_io_counters()
        metrics['network'] = {
            'bytes_sent': network_io.bytes_sent,
            'bytes_recv': network_io.bytes_recv,
            'packets_sent': network_io.packets_sent,
            'packets_recv': network_io.packets_recv
        }
        
        # 进程监控（获取CPU使用率最高的前5个进程）
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        # 按CPU使用率排序，取前5
        top_processes = sorted(processes, key=lambda x: x['cpu_percent'] or 0, reverse=True)[:5]
        metrics['top_processes'] = top_processes
        
        # 保存监控结果到文件
        metrics_file = parameters.get('metrics_file', 'logs/system_metrics.json')
        self.save_result(metrics, metrics_file)
        
        # 如果有告警，发送通知
        if metrics['alerts']:
            alert_message = '\n'.join(metrics['alerts'])
            notification_config = parameters.get('notification', {})
            self.send_notification(alert_message, notification_config)
            
        return True


# 为了向后兼容，保留原有的execute函数接口
def execute(parameters: Dict[str, Any], logger) -> bool:
    """向后兼容的执行函数"""
    task = SystemMonitorTask()
    return task.execute(parameters, logger)