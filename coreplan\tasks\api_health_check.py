"""
API健康检查任务模块 - 重构版本
定期检查指定API端点的可用性和响应时间
"""

import requests
import time
from datetime import datetime
from typing import Dict, Any, List

from .base_task import BaseTask


class ApiHealthCheckTask(BaseTask):
    """接口健康检查任务"""
    
    def __init__(self):
        super().__init__("接口健康检查")
        
    def run_task(self, parameters: Dict[str, Any]) -> bool:
        """执行接口健康检查任务"""
        endpoints = parameters.get('endpoints', [])
        if not endpoints:
            self.logger.warning("没有配置需要检查的API端点")
            return True
            
        results = {
            'timestamp': datetime.now().isoformat(),
            'checks': [],
            'failed_count': 0,
            'total_count': len(endpoints)
        }
        
        for endpoint in endpoints:
            check_result = self._check_endpoint(endpoint)
            results['checks'].append(check_result)
            
            if not check_result['success']:
                results['failed_count'] += 1
                
        # 保存检查结果
        results_file = parameters.get('results_file', 'logs/api_health_results.json')
        self.save_result(results, results_file, 200)
        
        # 如果有失败的检查，发送通知
        if results['failed_count'] > 0:
            failed_checks = [check for check in results['checks'] if not check['success']]
            message = self._build_failure_message(failed_checks)
            notification_config = parameters.get('notification', {})
            self.send_notification(message, notification_config)
            
        success_rate = ((results['total_count'] - results['failed_count']) / results['total_count']) * 100
        self.logger.info(f"接口健康检查完成，成功率: {success_rate:.1f}% ({results['total_count'] - results['failed_count']}/{results['total_count']})")
        
        return True
        
    def _check_endpoint(self, endpoint: Dict[str, Any]) -> Dict[str, Any]:
        """检查单个接口端点"""
        url = endpoint.get('url')
        timeout = endpoint.get('timeout', 10)
        method = endpoint.get('method', 'GET').upper()
        headers = endpoint.get('headers', {})
        expected_status = endpoint.get('expected_status', 200)
        
        result = {
            'url': url,
            'method': method,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'status_code': None,
            'response_time': None,
            'error': None
        }
        
        try:
            start_time = time.time()
            
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=timeout)
            elif method == 'POST':
                data = endpoint.get('data', {})
                response = requests.post(url, json=data, headers=headers, timeout=timeout)
            elif method == 'HEAD':
                response = requests.head(url, headers=headers, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
            response_time = (time.time() - start_time) * 1000
            
            result['status_code'] = response.status_code
            result['response_time'] = round(response_time, 2)
            
            # 检查状态码
            if response.status_code == expected_status:
                result['success'] = True
                self.logger.info(f"✓ {url} - {response.status_code} - {response_time:.2f}ms")
            else:
                result['error'] = f"状态码不匹配，期望: {expected_status}, 实际: {response.status_code}"
                self.logger.warning(f"✗ {url} - {result['error']}")
                
            # 检查响应内容（如果配置了）
            expected_content = endpoint.get('expected_content')
            if expected_content and result['success']:
                if expected_content not in response.text:
                    result['success'] = False
                    result['error'] = f"响应内容不包含期望的文本: {expected_content}"
                    self.logger.warning(f"✗ {url} - {result['error']}")
                    
        except requests.exceptions.Timeout:
            result['error'] = f"请求超时 (>{timeout}秒)"
            self.logger.error(f"✗ {url} - {result['error']}")
        except requests.exceptions.ConnectionError:
            result['error'] = "连接失败"
            self.logger.error(f"✗ {url} - {result['error']}")
        except requests.exceptions.RequestException as e:
            result['error'] = f"请求异常: {str(e)}"
            self.logger.error(f"✗ {url} - {result['error']}")
        except Exception as e:
            result['error'] = f"未知错误: {str(e)}"
            self.logger.error(f"✗ {url} - {result['error']}")
            
        return result
        
    def _build_failure_message(self, failed_checks: List[Dict[str, Any]]) -> str:
        """构建失败通知消息"""
        message = f"接口健康检查发现 {len(failed_checks)} 个失败的端点:\n\n"
        
        for check in failed_checks:
            message += f"• {check['url']}\n"
            message += f"  错误: {check['error']}\n"
            if check['status_code']:
                message += f"  状态码: {check['status_code']}\n"
            message += "\n"
            
        return message


# 为了向后兼容，保留原有的execute函数接口
def execute(parameters: Dict[str, Any], logger) -> bool:
    """向后兼容的执行函数"""
    task = ApiHealthCheckTask()
    return task.execute(parameters, logger)