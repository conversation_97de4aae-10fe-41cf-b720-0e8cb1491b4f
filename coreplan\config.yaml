# 定时任务系统配置文件

# 全局设置
global:
  log_level: "INFO"
  log_file: "logs/scheduler.log"
  log_max_size: "10MB" 
  log_backup_count: 5
  check_interval: 60
  max_concurrent_tasks: 5

# 任务配置
tasks:
  # 数据备份任务
  data_backup:
    enabled: false
    module: "tasks.data_backup"
    schedule:
      type: "cron"
      expression: "0 0 2 * * *"  # 每天凌晨2点
    parameters:
      source_paths: ["/data"]
      backup_path: "/backup" 
      retention_days: 7
    
  # 系统监控任务
  system_monitor:
    enabled: true
    module: "tasks.system_monitor"
    schedule:
      type: "interval"
      interval: 300  # 每5分钟
    parameters:
      alert_threshold: 80
      metrics_file: "logs/system_metrics.json"
    
  # 临时文件清理
  cleanup_temp:
    enabled: true
    module: "tasks.cleanup_temp"
    schedule:
      type: "cron"
      expression: "0 0 2 * * *"  # 每天凌晨2点
    parameters:
      temp_dirs: ["/tmp"]
      max_age_days: 3
      
  # API健康检查
  api_health_check:
    enabled: true
    module: "tasks.api_health_check"
    schedule:
      type: "interval"
      interval: 300  # 每5分钟
    parameters:
      endpoints:
        - url: "http://localhost:8080/health"
          timeout: 10
      results_file: "logs/api_health_results.json"

  # 设备时钟同步
  device_time_sync:
    enabled: true
    module: "tasks.device_time_sync"
    schedule:
      type: "cron"
      expression: "0 0 6 * * *"  # 每天06:00
    parameters:
      mysql:
        host: "localhost"
        port: 3306
        user: "root"
        password: ""
        database: "your_database"
        charset: "utf8mb4"
      emqx:
        api_url: "http://************:18083"
        api_username: "3c3ac0dc8e6a2c99"
        api_password: "9CM89Aiczt7eyGARk8WlXcpnDV6p0uW5vFDXMG4Om9AYnN"
        timeout: 30
        batch_size: 100