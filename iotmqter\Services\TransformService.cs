using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using iotmqter.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;
using System.Net.Http;
using System.Linq;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace iotmqter.Services
{
    /// <summary>
    /// 转换服务实现，负责消息格式转换和处理
    /// </summary>
    public class TransformService : ITransformService
    {
        private readonly ILogger<TransformService> _logger;
        private readonly IMqttService _mqttService;
        private readonly GrpIdMapper _grpIdMapper;
        private readonly IServiceProvider _serviceProvider;
        private readonly IRedisService _redisService;
        private readonly IHttpClientFactory _httpClientFactory;
        
        // 需要订阅的主题列表
        private static readonly string[] SubscriptionTopics = new[]
        {
            "v1/device/+/rpc/relay_ack",  // 设备响应主题（新格式）
            "device/config/#",            // 设备配置消息 - 更新tb_device.attributes
            "device/report/#",            // 设备上报消息 - 更新tb_device.telemetry
            "v1/vftkt/+/rpc/request",     // 北电云表请求主题
            "$SYS/brokers/+/clients/+/connected",    // 设备连接事件
            "$SYS/brokers/+/clients/+/disconnected"  // 设备断开连接事件
        };
        
        /// <summary>
        /// 初始化转换服务
        /// </summary>
        public TransformService(
            ILogger<TransformService> logger,
            IMqttService mqttService,
            GrpIdMapper grpIdMapper,
            IServiceProvider serviceProvider,
            IRedisService redisService,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger;
            _mqttService = mqttService;
            _grpIdMapper = grpIdMapper;
            _serviceProvider = serviceProvider;
            _redisService = redisService;
            _httpClientFactory = httpClientFactory;
            
            try
            {
                // 订阅MQTT消息接收事件
                _mqttService.MessageReceived += OnMessageReceivedAsync;
                
                // 初始化后立即执行订阅主题
                _ = Task.Run(SubscribeToTopicsAsync);
                
                _logger.LogInformation("TransformService已初始化并准备订阅MQTT消息事件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TransformService初始化失败");
                throw;
            }
        }
        
        /// <summary>
        /// 异步执行数据库操作 - SqlSugarScope单例在不同上下文中自动线程安全
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">数据库操作函数</param>
        /// <returns>操作结果</returns>
        private async Task<T> ExecuteDatabaseOperationAsync<T>(Func<ISqlSugarClient, Task<T>> operation)
        {
            using var scope = _serviceProvider.CreateScope();
            var db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
            return await operation(db);
        }
        
        /// <summary>
        /// 订阅所有需要的主题
        /// </summary>
        private async Task SubscribeToTopicsAsync()
        {
            try
            {
                _logger.LogInformation("开始订阅MQTT主题...");
                
                // 等待MQTT服务连接就绪
                int retries = 0;
                while (!_mqttService.IsConnected && retries < 10)
                {
                    _logger.LogInformation("等待MQTT服务连接就绪 ({Retry}/10)...", ++retries);
                    await Task.Delay(500 * retries); // 递增等待时间
                }
                
                if (!_mqttService.IsConnected)
                {
                    _logger.LogWarning("MQTT服务未连接，暂时无法订阅主题");
                    return;
                }
                
                // 订阅所有主题
                foreach (var topic in SubscriptionTopics)
                {
                    // qos=1保证不丢包
                    await _mqttService.SubscribeAsync(topic, 1);
                    _logger.LogInformation("已订阅主题: {Topic}", topic);
                }
                
                _logger.LogInformation("所有必要的MQTT主题订阅完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "订阅MQTT主题时发生错误");
            }
        }
        
        /// <summary>
        /// 处理接收到的MQTT消息
        /// </summary>
        private async Task OnMessageReceivedAsync(string topic, string payload)
        {
            try
            {
                _logger.LogDebug("收到消息处理请求: Topic={Topic}, Payload={Payload}",  topic, payload);
                
                // 1. 新版设备响应主题处理 - v1/device/{imei}/rpc/relay_ack
                if (topic.StartsWith("v1/device/") && topic.EndsWith("/rpc/relay_ack"))
                {
                    await HandleDeviceRpcRelayAsync(topic, payload);
                }
                // 2. 设备配置消息规则
                else if (topic.StartsWith("device/config/"))
                {
                    await HandleDeviceConfigMessageAsync(topic, payload);
                }
                // 3. 设备上报消息规则
                else if (topic.StartsWith("device/report/"))
                {
                    await HandleDeviceReportMessageAsync(topic, payload);
                }
                // 4. 北电云表请求消息
                else if (topic.StartsWith("v1/vftkt/") && topic.EndsWith("/rpc/request"))
                {
                    await HandleVftktRequestAsync(topic, payload);
                }
                // 5. 设备连接事件
                else if (topic.Contains("/clients/") && topic.EndsWith("/connected"))
                {
                    await HandleDeviceConnectedAsync(topic, payload);
                }
                // 6. 设备断开连接事件
                else if (topic.Contains("/clients/") && topic.EndsWith("/disconnected"))
                {
                    await HandleDeviceDisconnectedAsync(topic, payload);
                }
                else
                {
                    _logger.LogWarning("未匹配到任何处理规则的主题: {Topic}", topic);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理MQTT消息时发生错误: Topic={Topic}, Payload={Payload}", topic, payload);
            }
        }
        
        /// <summary>
        /// 处理新版设备RPC响应消息 - v1/device/{imei}/rpc/relay
        /// </summary>
        private async Task HandleDeviceRpcRelayAsync(string topic, string payload)
        {
            try
            {
                _logger.LogDebug("处理新版设备RPC响应消息: Topic={Topic}", topic);
                
                // 解析主题提取IMEI - 格式: v1/device/{imei}/rpc/relay
                string[] topicParts = topic.Split('/');
                if (topicParts.Length < 4)
                {
                    _logger.LogWarning("设备RPC响应主题格式不正确: {Topic}", topic);
                    return;
                }
                
                string imei = topicParts[2]; // 从主题中提取IMEI
                
                // 解析原始消息
                var json = JObject.Parse(payload);
                string? taskId = json["taskid"]?.Value<string>();
                
                if (string.IsNullOrEmpty(taskId))
                {
                    _logger.LogWarning("设备RPC响应消息中缺少taskid字段: {Payload}", payload);
                    return;
                }
                
                _logger.LogDebug("从主题提取IMEI: {Imei}, 从消息体提取TaskId: {TaskId}", imei, taskId);
                
                // 写入redis
                await HandleDeviceResponseToRedisAsync(taskId, topic, payload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理新版设备RPC响应消息时发生错误: {Topic}, {Payload}", topic, payload);
            }
        }

        /// <summary>
        /// 处理设备配置消息，更新tb_device.attributes字段
        /// </summary>
        private async Task HandleDeviceConfigMessageAsync(string topic, string payload)
        {
            try
            {
                _logger.LogDebug("处理设备配置消息: Topic={Topic}, Payload={Payload}", topic, payload);
                
                // 从主题中提取clientId - 格式为device/config/${clientid}
                string[] topicParts = topic.Split('/');
                if (topicParts.Length < 3)
                {
                    _logger.LogWarning("设备配置主题格式错误: {Topic}", topic);
                    return;
                }
                
                string clientId = topicParts[2];
                
                if (string.IsNullOrEmpty(clientId))
                {
                    _logger.LogWarning("无法从主题中提取客户端ID: {Topic}", topic);
                    return;
                }
                
                
                // 解析JSON获取product、version和branchv字段
                string product = string.Empty;
                string version = string.Empty;
                string branchv = string.Empty;
                bool hasProductInfo = false;
                
                try
                {
                    var pjson = JObject.Parse(payload);
                    product = pjson["product"]?.Value<string>() ?? "";
                    version = pjson["version"]?.Value<string>() ?? "";
                    branchv = pjson["branchv"]?.Value<string>() ?? "";
                    
                    hasProductInfo = !string.IsNullOrEmpty(product) || !string.IsNullOrEmpty(version) || !string.IsNullOrEmpty(branchv);
                    
                    _logger.LogInformation("从设备配置中提取信息: ClientId={ClientId}, Product={Product}, Version={Version}, BranchV={BranchV}", 
                        clientId, product, version, branchv);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "解析设备配置JSON失败: {Payload}", payload);
                }
                
                // 查询设备信息并更新属性
                var (deviceInfo, result) = await ExecuteDatabaseOperationAsync(async (db) =>
                {
                    // 查询设备信息，获取unilink
                    var device = await db.Queryable<DeviceEntity>()
                        .Where(d => d.ClientId == clientId)
                        .FirstAsync();
                    
                    // 构建更新语句，避免SqlSugar表达式错误
                    var updateable = db.Updateable<DeviceEntity>()
                        .SetColumns(d => new DeviceEntity
                        {
                            Attributes = payload,
                            UpdateTime = DateTime.Now
                        });

                    // 根据是否有产品信息决定更新哪些字段
                    if (!string.IsNullOrEmpty(product))
                    {
                        updateable = updateable.SetColumns(d => d.Product, product);
                    }
                    if (!string.IsNullOrEmpty(version))
                    {
                        updateable = updateable.SetColumns(d => d.Version, version);
                    }
                    if (!string.IsNullOrEmpty(branchv))
                    {
                        updateable = updateable.SetColumns(d => d.BranchV, branchv);
                    }

                    var updateResult = await updateable
                        .Where(d => d.ClientId == clientId)
                        .ExecuteCommandAsync();
                    
                    return (device, updateResult);
                });
                
                if (result > 0)
                {
                    if (hasProductInfo)
                        _logger.LogInformation("成功更新设备 {ClientId} 的attributes和版本信息", clientId);
                    else
                        _logger.LogInformation("成功更新设备 {ClientId} 的attributes", clientId);
                }
                else
                {
                    _logger.LogWarning("未找到设备 {ClientId} 或更新失败", clientId);
                }
                
                // 如果设备存在且unilink不为空，则转发到unilink主题
                if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Unilink))
                {
                    // 去掉clientId前缀"i"
                    string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                    
                    string unilinkTopic = $"v1/unilink/{deviceInfo.Unilink}/{cleanClientId}/config";
                    await _mqttService.PublishMessageAsync(unilinkTopic, payload);
                    _logger.LogInformation("已转发设备配置到unilink主题: Topic={Topic}, ClientId={ClientId}, Unilink={Unilink}", 
                        unilinkTopic, cleanClientId, deviceInfo.Unilink);
                }
                
                // 如果设备存在且dispatch不为空，则转发到dispatch主题
                if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Dispatch))
                {
                    // 去掉clientId前缀"i"
                    string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                    
                    string dispatchTopic = $"v1/dispatch/{deviceInfo.Dispatch}/{cleanClientId}/config";
                    await _mqttService.PublishMessageAsync(dispatchTopic, payload);
                    _logger.LogInformation("已转发设备配置到dispatch主题: Topic={Topic}, ClientId={ClientId}, Dispatch={Dispatch}", 
                        dispatchTopic, cleanClientId, deviceInfo.Dispatch);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备配置消息时发生异常: {Topic}", topic);
            }
        }

        /// <summary>
        /// 处理设备上报消息，更新tb_device.telemetry字段
        /// </summary>
        private async Task HandleDeviceReportMessageAsync(string topic, string payload)
        {
            try
            {
                _logger.LogDebug("处理设备上报消息: Topic={Topic}, Payload={Payload}", topic, payload);
                
                // 从主题中提取clientId - 格式为device/report/${clientid}
                string[] topicParts = topic.Split('/');
                if (topicParts.Length < 3)
                {
                    _logger.LogWarning("设备上报主题格式错误: {Topic}", topic);
                    return;
                }
                
                string clientId = topicParts[2];
                
                if (string.IsNullOrEmpty(clientId))
                {
                    _logger.LogWarning("无法从主题中提取客户端ID: {Topic}", topic);
                    return;
                }
                
                
                // 查询设备信息并更新遥测数据
                var (deviceInfo, result) = await ExecuteDatabaseOperationAsync(async (db) =>
                {
                    // 查询设备信息，获取unilink
                    var device = await db.Queryable<DeviceEntity>()
                        .Where(d => d.ClientId == clientId)
                        .FirstAsync();
                    
                    // 更新设备遥测数据
                    var updateResult = await db.Updateable<DeviceEntity>()
                        .SetColumns(d => new DeviceEntity
                        {
                            Telemetry = payload,
                            UpdateTime = DateTime.Now
                        })
                        .Where(d => d.ClientId == clientId)
                        .ExecuteCommandAsync();
                    
                    return (device, updateResult);
                });
                    
                if (result > 0)
                {
                    _logger.LogInformation("成功更新设备 {ClientId} 的telemetry", clientId);
                }
                else
                {
                    _logger.LogWarning("未找到设备 {ClientId} 或更新telemetry失败", clientId);
                }
                
                // 如果设备存在且unilink不为空，则转发到unilink主题
                if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Unilink))
                {
                    // 去掉clientId前缀"i"
                    string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                    
                    string unilinkTopic = $"v1/unilink/{deviceInfo.Unilink}/{cleanClientId}/report";
                    await _mqttService.PublishMessageAsync(unilinkTopic, payload);
                    _logger.LogInformation("已转发设备上报数据到unilink主题: Topic={Topic}, ClientId={ClientId}, Unilink={Unilink}", 
                        unilinkTopic, cleanClientId, deviceInfo.Unilink);
                }
                
                // 如果设备存在且dispatch不为空，则转发到dispatch主题
                if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Dispatch))
                {
                    // 去掉clientId前缀"i"
                    string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                    
                    string dispatchTopic = $"v1/dispatch/{deviceInfo.Dispatch}/{cleanClientId}/report";
                    await _mqttService.PublishMessageAsync(dispatchTopic, payload);
                    _logger.LogInformation("已转发设备上报数据到dispatch主题: Topic={Topic}, ClientId={ClientId}, Dispatch={Dispatch}", 
                        dispatchTopic, cleanClientId, deviceInfo.Dispatch);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备上报消息时发生异常: {Topic}", topic);
            }
        }

        /// <summary>
        /// 处理设备响应消息并写入Redis
        /// </summary>
        private async Task HandleDeviceResponseToRedisAsync(string taskId, string topic, string payload)
        {
            try
            {
                _logger.LogDebug("处理设备响应消息并写入Redis: Topic={Topic}, Payload={Payload}", topic, payload);
                
                // 添加兼容方法：直接发布Redis消息
                try
                {
                    string newTopic = $"tksrv.device/{taskId}/respone/relay";
                    await _redisService.PublishAsync(newTopic, payload);
                    _logger.LogInformation("已执行兼容方法，发布Redis消息: Channel={Channel}, Message={MessageLength}字节", newTopic, payload.Length);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "执行Redis.PublishAsync兼容方法时出错");
                }
                
                // 将taskId作为Redis键
                string redisKey = $"guid.{taskId}";
                
                // 发布消息到Redis通道
                await _redisService.PublishAsync(redisKey, payload);
                _logger.LogInformation("设备响应消息已发布到Redis通道: Channel={Channel}, Message={MessageLength}字节", redisKey, payload.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备响应消息写入Redis失败: Topic={Topic}", topic);
            }
        }

        /// <summary>
        /// 处理北电云表请求消息
        /// </summary>
        private async Task HandleVftktRequestAsync(string topic, string payload)
        {
            try
            {
                _logger.LogInformation("处理北电云表请求消息: Topic={Topic}", topic);
                _logger.LogDebug("北电云表请求消息内容: {Payload}", payload);

                // 提取unilink部分（主题格式：v1/vftkt/{unilink}/rpc/request）
                var topicParts = topic.Split('/');
                if (topicParts.Length < 4)
                {
                    _logger.LogWarning("北电云表请求主题格式不正确: {Topic}", topic);
                    return;
                }
                
                string unilink = topicParts[2];
                _logger.LogDebug("提取的unilink值为: {Unilink}", unilink);
                
                // 解析请求消息体，提取taskid
                var json = JObject.Parse(payload);
                
                // 检查是否包含taskid字段
                string? taskId = json["taskid"]?.Value<string>();
                if (string.IsNullOrEmpty(taskId))
                {
                    _logger.LogWarning("北电云表请求消息中缺少taskid字段或为空: {Payload}", payload);
                    return;
                }
                
                _logger.LogDebug("提取的taskId值为: {TaskId}", taskId);
                
                // 获取北电云表令牌
                string token = await GetBdybTokenAsync();
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogError("无法获取北电云表令牌，无法转发请求");
                    return;
                }
                
                // 从环境变量获取北电云表API URL
                string bdybUrl = Environment.GetEnvironmentVariable("BDYB_URL") ?? "";
                if (string.IsNullOrEmpty(bdybUrl))
                {
                    _logger.LogError("未配置BDYB_URL环境变量，无法转发请求");
                    return;
                }
                
                // 构建完整的API URL
                string apiUrl = $"{bdybUrl}/api/device/command";
                
                // 发送HTTP请求
                using var httpClient = _httpClientFactory.CreateClient("default");
                httpClient.DefaultRequestHeaders.Add("token", token);
                
                // 发送请求体
                var response = await httpClient.PostAsync(
                    apiUrl,
                    new StringContent(payload, Encoding.UTF8, "application/json")
                );
                
                // 检查响应状态
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("北电云表API请求失败，状态码: {StatusCode}", (int)response.StatusCode);
                    return;
                }
                
                // 解析响应内容
                string responseBody = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("北电云表API响应: {Response}", responseBody);
                
                var responseJson = JObject.Parse(responseBody);
                
                // 检查响应状态码
                int? code = responseJson["code"]?.Value<int>();
                if (code != 0)
                {
                    _logger.LogError("北电云表API返回错误: {Response}", responseBody);
                    return;
                }
                
                // 提取commandId
                string? commandId = responseJson["commandId"]?.Value<string>();
                if (string.IsNullOrEmpty(commandId))
                {
                    _logger.LogWarning("北电云表API响应中commandId为空: {Response}", responseBody);
                    return;
                }
                
                _logger.LogInformation("成功获取北电云表commandId: {CommandId}", commandId);
                
                // 将commandId和taskId的映射关系写入Redis，有效期30秒
                const int expireSeconds = 30;
                await _redisService.StringSetAsync($"cdata.bdyb.{commandId}", taskId, expireSeconds);
                _logger.LogInformation("已将commandId与taskId的映射写入Redis: CommandId={CommandId}, TaskId={TaskId}, 有效期={ExpireSeconds}秒", 
                    commandId, taskId, expireSeconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理北电云表请求消息时发生错误: Topic={Topic}", topic);
            }
        }
        
        /// <summary>
        /// 获取北电云表令牌
        /// </summary>
        private async Task<string> GetBdybTokenAsync()
        {
            try
            {
                // 获取令牌URL
                string tokenUrl = (Environment.GetEnvironmentVariable("API_URL") ?? "http://127.0.0.1:80") +  "/oauth/bdyb";
                _logger.LogDebug("获取北电云表令牌URL: {TokenUrl}", tokenUrl);
                
                using var httpClient = _httpClientFactory.CreateClient("default");
                var response = await httpClient.GetAsync(tokenUrl);
                
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("获取北电云表令牌失败，状态码: {StatusCode}", (int)response.StatusCode);
                    return string.Empty;
                }
                
                string responseBody = await response.Content.ReadAsStringAsync();
                var json = JObject.Parse(responseBody);
                
                return json["token"]?.Value<string>() ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取北电云表令牌时发生异常");
                return string.Empty;
            }
        }

        /// <summary>
        /// 处理设备连接事件
        /// </summary>
        private async Task HandleDeviceConnectedAsync(string topic, string payload)
        {
            try
            {
                _logger.LogInformation("处理设备连接事件: Topic={Topic}", topic);
                
                // 解析主题以获取客户端ID - 格式为$SYS/brokers/{brokerid}/clients/{clientid}/connected
                string[] topicParts = topic.Split('/');
                if (topicParts.Length < 5)
                {
                    _logger.LogWarning("设备连接主题格式错误: {Topic}", topic);
                    return;
                }
                
                string clientId = topicParts[4];
                
                if (string.IsNullOrEmpty(clientId))
                {
                    _logger.LogWarning("无法从主题中提取客户端ID: {Topic}", topic);
                    return;
                }
                
                // 解析连接消息
                var connectEvent = JsonConvert.DeserializeObject<MqttClientConnectedEvent>(payload);
                if (connectEvent == null)
                {
                    _logger.LogWarning("无法解析设备连接事件: {Payload}", payload);
                    return;
                }
                
                _logger.LogInformation("收到客户端连接事件：{ClientId}, 客户端地址：{PeerName}", clientId, connectEvent.IpAddress);
                
                
                // 查询设备信息并更新连接状态
                var (deviceInfo, result) = await ExecuteDatabaseOperationAsync(async (db) =>
                {
                    // 先查询设备信息，获取unilink和dispatch信息
                    var device = await db.Queryable<DeviceEntity>()
                        .Where(d => d.ClientId == clientId)
                        .FirstAsync();

                    // 更新设备在线状态和连接时间
                    var updateResult = await db.Updateable<DeviceEntity>()
                        .SetColumns(d => new DeviceEntity
                        {
                            IsOnline = 1,
                            ConnectTime = DateTime.Now
                        })
                        .Where(d => d.ClientId == clientId)
                        .ExecuteCommandAsync();
                    
                    return (device, updateResult);
                });
                
                if (result > 0)
                {
                    _logger.LogInformation("成功更新客户端 {ClientId} 的连接状态", clientId);
                   
                    // 如果设备存在且unilink不为空，则发布到unilink主题
                    if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Unilink))
                    {
                        // 去掉clientId前缀"i"
                        string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                        
                        string unilinkTopic = $"v1/unilink/{deviceInfo.Unilink}/{cleanClientId}/client_connected";
                        await _mqttService.PublishMessageAsync(unilinkTopic, payload);
                        _logger.LogInformation("已推送连接事件到unilink主题: {Topic}", unilinkTopic);
                    }
                    
                    // 如果设备存在且dispatch不为空，则发布到dispatch主题
                    if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Dispatch))
                    {
                        // 去掉clientId前缀"i"
                        string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;

                        string dispatchTopic = $"v1/dispatch/{deviceInfo.Dispatch}/{cleanClientId}/client_connected";
                        await _mqttService.PublishMessageAsync(dispatchTopic, payload);
                        _logger.LogInformation("已推送连接事件到dispatch主题: {Topic}", dispatchTopic);
                    }
                }
                else
                {
                    _logger.LogWarning("未找到客户端 {ClientId} 或更新连接状态失败", clientId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备连接事件时发生异常: {Topic}, {Payload}", topic, payload);
            }
        }
        
        /// <summary>
        /// 处理设备断开连接事件
        /// </summary>
        private async Task HandleDeviceDisconnectedAsync(string topic, string payload)
        {
            try
            {
                _logger.LogInformation("处理设备断开连接事件: Topic={Topic}", topic);
                
                // 解析主题以获取客户端ID - 格式为$SYS/brokers/{brokerid}/clients/{clientid}/disconnected
                string[] topicParts = topic.Split('/');
                if (topicParts.Length < 5)
                {
                    _logger.LogWarning("设备断开连接主题格式错误: {Topic}", topic);
                    return;
                }
                
                string clientId = topicParts[4];
                
                if (string.IsNullOrEmpty(clientId))
                {
                    _logger.LogWarning("无法从主题中提取客户端ID: {Topic}", topic);
                    return;
                }
                
                // 解析断开连接消息
                var disconnectEvent = JsonConvert.DeserializeObject<MqttClientDisconnectedEvent>(payload);
                if (disconnectEvent == null)
                {
                    _logger.LogWarning("无法解析设备断开连接事件: {Payload}", payload);
                    return;
                }
                
                _logger.LogInformation("收到客户端断开连接事件：{ClientId}, 原因：{Reason}", clientId, disconnectEvent.Reason);
                
                
                // 查询设备信息并更新断开连接状态
                var (deviceInfo, result) = await ExecuteDatabaseOperationAsync(async (db) =>
                {
                    // 先查询设备信息，获取unilink和dispatch信息
                    var device = await db.Queryable<DeviceEntity>()
                        .Where(d => d.ClientId == clientId)
                        .FirstAsync();
                        
                    // 更新设备离线状态、断开连接时间和原因
                    var updateResult = await db.Updateable<DeviceEntity>()
                        .SetColumns(d => new DeviceEntity
                        {
                            IsOnline = 0,
                            DisconnectTime = DateTime.Now,
                            DisconnectReason = disconnectEvent.Reason
                        })
                        .Where(d => d.ClientId == clientId)
                        .ExecuteCommandAsync();
                    
                    return (device, updateResult);
                });
                
                if (result > 0)
                {
                    _logger.LogInformation("成功更新客户端 {ClientId} 的断开连接状态", clientId);
                    
                    // 如果设备存在且unilink不为空，则发布到unilink主题
                    if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Unilink))
                    {
                        // 去掉clientId前缀"i"
                        string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;
                        
                        string unilinkTopic = $"v1/unilink/{deviceInfo.Unilink}/{cleanClientId}/client_disconnected";
                        await _mqttService.PublishMessageAsync(unilinkTopic, payload);
                        _logger.LogInformation("已推送断开连接事件到unilink主题: {Topic}", unilinkTopic);
                    }
                    
                    // 如果设备存在且dispatch不为空，则发布到dispatch主题
                    if (deviceInfo != null && !string.IsNullOrEmpty(deviceInfo.Dispatch))
                    {
                        // 去掉clientId前缀"i"
                        string cleanClientId = clientId.StartsWith("i") ? clientId.Substring(1) : clientId;

                        string unilinkTopic = $"v1/dispatch/{deviceInfo.Dispatch}/{cleanClientId}/client_disconnected";
                        await _mqttService.PublishMessageAsync(unilinkTopic, payload);
                        _logger.LogInformation("已推送断开连接事件到dispatch主题: {Topic}", unilinkTopic);
                    }
                }
                else
                {
                    _logger.LogWarning("未找到客户端 {ClientId} 或更新断开连接状态失败", clientId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设备断开连接事件时发生异常: {Topic}, {Payload}", topic, payload);
            }
        }
    }
} 